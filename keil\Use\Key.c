#include "ti_msp_dl_config.h"
#include "Key.h"
#include "Systick.h"

// 按键扫描函数 - 完善版本，支持长短按检测
KeyState_t Key_Scan(void)
{
    static uint8_t key3_raw = 1, key4_raw = 1;         // 原始GPIO状态
    static uint8_t key3_stable = 1, key4_stable = 1;   // 消抖后稳定状态
    static uint8_t key3_last = 1, key4_last = 1;       // 上次稳定状态
    static uint8_t key3_debounce = 0, key4_debounce = 0; // 消抖计数
    static uint16_t key3_press_time = 0, key4_press_time = 0; // 按下时间计数
    static uint8_t key3_long_sent = 0, key4_long_sent = 0;   // 长按已发送标志

    KeyState_t result = KEY_NONE;

    // 读取按键原始状态(低电平有效)
    key3_raw = DL_GPIO_readPins(GPIO_KEY_PORT, GPIO_KEY_PIN_KEY3_PIN) ? 1 : 0;
    key4_raw = DL_GPIO_readPins(GPIO_KEY_PORT, GPIO_KEY_PIN_KEY4_PIN) ? 1 : 0;

    // KEY3消抖处理
    if(key3_raw == key3_stable) {
        key3_debounce = 0; // 状态一致，清零消抖计数
    } else {
        key3_debounce++;
        if(key3_debounce >= 5) { // 5ms消抖
            key3_stable = key3_raw;
            key3_debounce = 0;
        }
    }

    // KEY4消抖处理
    if(key4_raw == key4_stable) {
        key4_debounce = 0;
    } else {
        key4_debounce++;
        if(key4_debounce >= 5) { // 5ms消抖
            key4_stable = key4_raw;
            key4_debounce = 0;
        }
    }

    // KEY3状态处理
    if(key3_stable == 0 && key3_last == 1) {
        // 按键刚按下
        key3_press_time = 0;
        key3_long_sent = 0;
    } else if(key3_stable == 0 && key3_last == 0) {
        // 按键持续按下
        key3_press_time++;
        if(key3_press_time >= 1000 && key3_long_sent == 0) {
            // 长按1秒触发
            key3_long_sent = 1;
            result = KEY3_LONG;
        }
    } else if(key3_stable == 1 && key3_last == 0) {
        // 按键释放
        if(key3_press_time >= 20 && key3_long_sent == 0) {
            // 短按(20ms以上且未触发长按)
            result = KEY3_SHORT;
        }
        key3_press_time = 0;
        key3_long_sent = 0;
    }

    // KEY4状态处理(只有在KEY3没有事件时才处理，避免冲突)
    if(result == KEY_NONE) {
        if(key4_stable == 0 && key4_last == 1) {
            // 按键刚按下
            key4_press_time = 0;
            key4_long_sent = 0;
        } else if(key4_stable == 0 && key4_last == 0) {
            // 按键持续按下
            key4_press_time++;
            if(key4_press_time >= 1000 && key4_long_sent == 0) {
                // 长按1秒触发
                key4_long_sent = 1;
                result = KEY4_LONG;
            }
        } else if(key4_stable == 1 && key4_last == 0) {
            // 按键释放
            if(key4_press_time >= 20 && key4_long_sent == 0) {
                // 短按(20ms以上且未触发长按)
                result = KEY4_SHORT;
            }
            key4_press_time = 0;
            key4_long_sent = 0;
        }
    }

    // 更新上次状态
    key3_last = key3_stable;
    key4_last = key4_stable;

    return result;
}

// 按键测试函数 - 演示如何使用Key_Scan
void Key_Test_Demo(void)
{
    KeyState_t key_result = Key_Scan();

    switch(key_result) {
        case KEY3_SHORT:
            // KEY3短按 - LED快闪2次
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(100);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(100);
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(100);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            break;

        case KEY3_LONG:
            // KEY3长按 - LED长亮1秒
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(1000);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            break;

        case KEY4_SHORT:
            // KEY4短按 - LED快闪1次
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(200);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            break;

        case KEY4_LONG:
            // KEY4长按 - LED慢闪3次
            for(int i = 0; i < 3; i++) {
                DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
                mspm0_delay_ms(300);
                DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
                mspm0_delay_ms(300);
            }
            break;

        case KEY_NONE:
        default:
            // 无按键操作
            break;
    }
}
