#include "ti_msp_dl_config.h"
#include "Key.h"
#include "Systick.h"

// 按键扫描函数 - 支持长短按检测
KeyState_t Key_Scan(void)
{
    static uint8_t key3_state = 1, key4_state = 1;     // 按键当前状态(1=未按下,0=按下)
    static uint8_t key3_last = 1, key4_last = 1;       // 按键上次状态
    static uint16_t key3_cnt = 0, key4_cnt = 0;        // 按键计数器
    static uint8_t key3_flag = 0, key4_flag = 0;       // 长按标志

    // 读取按键物理状态(低电平有效)
    key3_state = DL_GPIO_readPins(GPIO_KEY_PORT, GPIO_KEY_PIN_KEY3_PIN) ? 1 : 0;
    key4_state = DL_GPIO_readPins(GPIO_KEY_PORT, GPIO_KEY_PIN_KEY4_PIN) ? 1 : 0;

    // KEY3处理
    if(key3_state == 0 && key3_last == 1) {
        // 按键刚按下，开始计时
        key3_cnt = 0;
        key3_flag = 0;
    } else if(key3_state == 0 && key3_last == 0) {
        // 按键持续按下，计数增加
        key3_cnt++;
        if(key3_cnt >= 1000 && key3_flag == 0) {
            // 长按1秒触发
            key3_flag = 1;
            key3_last = key3_state;
            return KEY3_LONG;
        }
    } else if(key3_state == 1 && key3_last == 0) {
        // 按键释放
        if(key3_cnt >= 20 && key3_cnt < 1000 && key3_flag == 0) {
            // 短按(20ms-1000ms)
            key3_last = key3_state;
            return KEY3_SHORT;
        }
    }

    // KEY4处理
    if(key4_state == 0 && key4_last == 1) {
        // 按键刚按下，开始计时
        key4_cnt = 0;
        key4_flag = 0;
    } else if(key4_state == 0 && key4_last == 0) {
        // 按键持续按下，计数增加
        key4_cnt++;
        if(key4_cnt >= 1000 && key4_flag == 0) {
            // 长按1秒触发
            key4_flag = 1;
            key4_last = key4_state;
            return KEY4_LONG;
        }
    } else if(key4_state == 1 && key4_last == 0) {
        // 按键释放
        if(key4_cnt >= 20 && key4_cnt < 1000 && key4_flag == 0) {
            // 短按(20ms-1000ms)
            key4_last = key4_state;
            return KEY4_SHORT;
        }
    }

    // 更新上次状态
    key3_last = key3_state;
    key4_last = key4_state;

    return KEY_NONE;
}

// 按键测试函数 - 演示如何使用Key_Scan
void Key_Test_Demo(void)
{
    KeyState_t key_result = Key_Scan();

    switch(key_result) {
        case KEY3_SHORT:
            // KEY3短按 - LED快闪2次
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(100);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(100);
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(100);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            break;

        case KEY3_LONG:
            // KEY3长按 - LED长亮1秒
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(1000);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            break;

        case KEY4_SHORT:
            // KEY4短按 - LED快闪1次
            DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            mspm0_delay_ms(200);
            DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
            break;

        case KEY4_LONG:
            // KEY4长按 - LED慢闪3次
            for(int i = 0; i < 3; i++) {
                DL_GPIO_setPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
                mspm0_delay_ms(300);
                DL_GPIO_clearPins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
                mspm0_delay_ms(300);
            }
            break;

        case KEY_NONE:
        default:
            // 无按键操作
            break;
    }
}
