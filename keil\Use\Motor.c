#include "ti_msp_dl_config.h"
#include "Motor.h"
#include "Gray.h"
//Motor1 ����
//Motor2 ����
//DR1  DR2  �� ��
// 0	  1  ������ǰ��
// 1    0  �����ֺ���
// 0    0	 ���ֺ�������ǰ�� ��ת
// 1    1  ����ǰ�����ֺ��� ��ת


static void setMotorSpeed(GPTIMER_Regs *timer, uint32_t speed_hz, DL_TIMER_CC_INDEX ccIndex)
{
    if (speed_hz == 0) {
        DL_TimerA_stopCounter(timer); // ֹͣ��ʱ��
        return;
    }

    // ��������ֵ������32��Ƶ��32MHzϵͳʱ��
    // ʵ��ʱ��Ƶ�� = 32MHz / 32 = 1MHz
    uint32_t period = PWM_MOTOR1_INST_CLK_FREQ / speed_hz; // 1MHz / speed_hz

    // ֹͣ��ʱ���԰�ȫ�޸Ĳ���
    DL_TimerA_stopCounter(timer);

    // �����µ�����ֵ
    DL_TimerA_setLoadValue(timer, period);

    // ���ñȽ�ֵ(50%ռ�ձ�)
    DL_TimerA_setCaptureCompareValue(timer, period / 2, ccIndex);

    // ����������ʱ��
    DL_TimerA_startCounter(timer);
}

// ���1���ƺ���
void Motor1_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR1_INST, speed_hz, GPIO_PWM_MOTOR1_C2_IDX);
}

// ���2���ƺ���
void Motor2_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR2_INST, speed_hz, GPIO_PWM_MOTOR2_C1_IDX);
}


void Motor_DifferentialControl(uint32_t base_speed, int16_t turn_value)
{
    uint32_t left_speed, right_speed;

    // �����������ٶȣ�turn_valueΪת��ƫ��ֵ
    // turn_value > 0: ��ת�����������ٶ�
    // turn_value < 0: ��ת�����������ٶ�
    // turn_value = 0: ֱ�У�����ͬ��

    if (turn_value > 0) {
        // ��ת�����ֱ��ֻ����ٶȣ����ּ���
        left_speed = base_speed;
        if (turn_value >= base_speed) {
            right_speed = 0; // ���ת��ʱ����ֹͣ
        } else {
            right_speed = base_speed - turn_value;
        }
    } else if (turn_value < 0) {
        // ��ת�����ֱ��ֻ����ٶȣ����ּ���
        right_speed = base_speed;
        if ((-turn_value) >= base_speed) {
            left_speed = 0; // ���ת��ʱ����ֹͣ
        } else {
            left_speed = base_speed + turn_value; // turn_valueΪ����
        }
    } else {
        // ֱ�У�����ͬ��
        left_speed = base_speed;
        right_speed = base_speed;
    }

    // ���õ���ٶ� (����Motor1Ϊ���֣�Motor2Ϊ����)
    Motor1_SetSpeed(left_speed);
    Motor2_SetSpeed(right_speed);
}

//// �򻯵ķ�����ƺ���
//void Motor_Move(uint32_t speed, int8_t direction)
//{
//    switch (direction) {
//        case 0: // ֱ��
//            Motor1_SetSpeed(speed);
//            Motor2_SetSpeed(speed);
//            break;
//        case 1: // ��ת
//            Motor1_SetSpeed(speed);
//            Motor2_SetSpeed(speed / 2); // ���ּ����ٶ�
//            break;
//        case -1: // ��ת
//            Motor1_SetSpeed(speed / 2); // ���ּ����ٶ�
//            Motor2_SetSpeed(speed);
//            break;
//        case 2: // ����ת
//            Motor1_SetSpeed(speed);
//            Motor2_SetSpeed(0); // ����ֹͣ
//            break;
//        case -2: // ����ת
//            Motor1_SetSpeed(0); // ����ֹͣ
//            Motor2_SetSpeed(speed);
//            break;
//        default: // ֹͣ
//            Motor1_SetSpeed(0);
//            Motor2_SetSpeed(0);
//            break;
//    }
//}

// ֹͣ���е��
void Motor_Stop(void)
{
    Motor1_SetSpeed(0);
    Motor2_SetSpeed(0);
}

// ���Ժ���: ���Ի��ȴ�����״̬
void Motor_Debug(void)
{
    static uint32_t debug_counter = 0;
    uint8_t gray = Read_GraySensors();
    int8_t pos = Calculate_LinePosition(gray);

    debug_counter++;

    // ÿ20�ε��ã�1�룩�л�һ��LED״̬����ʾϵͳ����
    if(debug_counter % 20 == 0) {
        DL_GPIO_togglePins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
    }

    // ǿ������ǰ����
    DL_GPIO_clearPins(GPIO_STEP_PORT, GPIO_STEP_DIR1_PIN); // DIR1=0
    DL_GPIO_setPins(GPIO_STEP_PORT, GPIO_STEP_DIR2_PIN);   // DIR2=1

    // ���ݴ�����״̬���ƶ���
    if(gray == 0) {
        // δ���⵽�ߣ�ֹͣ
        Motor1_SetSpeed(0);
        Motor2_SetSpeed(0);
    } else {
        // ���⵽�ߣ����ݲ�ͬλ�ý��в�ͬ����
        switch(pos) {
            case 0:  // ����
                Motor1_SetSpeed(400);
                Motor2_SetSpeed(400);
                break;
            case -1: case 1: // ��΢ƫ��
                if(pos < 0) {
                    Motor1_SetSpeed(300); // ��ת
                    Motor2_SetSpeed(400);
                } else {
                    Motor1_SetSpeed(400); // ��ת
                    Motor2_SetSpeed(300);
                }
                break;
            default: // ���ƫ��
                if(pos < 0) {
                    Motor1_SetSpeed(200); // ��ת
                    Motor2_SetSpeed(500);
                } else {
                    Motor1_SetSpeed(500); // ��ת
                    Motor2_SetSpeed(200);
                }
                break;
        }
    }
}

void Motor_Proc(void)
{
	uint8_t gray=0;
	int8_t temp=0;
	static uint8_t last_gray = 0; // 记录上次传感器状态
	static int8_t last_temp = 0;  // 记录上次位置

	gray=Read_GraySensors();
	temp=Calculate_LinePosition(gray);

	// 调试信息：检查传感器变化
	if(gray != last_gray) {
		// 传感器状态发生变化，LED闪烁指示
		DL_GPIO_togglePins(GPIO_LED_PORT, GPIO_LED_PIN_LED_PIN);
	}

	// 确保电机方向设置正确（前进方向）
	DL_GPIO_clearPins(GPIO_STEP_PORT, GPIO_STEP_DIR1_PIN); // DIR1=0
	DL_GPIO_setPins(GPIO_STEP_PORT, GPIO_STEP_DIR2_PIN);   // DIR2=1

	switch(temp)
	{
		//���Ĵ�����ƫ�����Ĵ�����ƫ
		case 0: // 居中
			Motor_DifferentialControl(300,0); // 提高基础速度
			break;
		case -1: // 轻微左偏
			Motor_DifferentialControl(300,-30); // 增加转向力度
			break;
		case 1: // 轻微右偏
			Motor_DifferentialControl(300,30);
			break;
		case -2: // 中等左偏
			Motor_DifferentialControl(250,-60);
			break;
		case 2: // 中等右偏
			Motor_DifferentialControl(250,60);
			break;
		case -3: // 严重左偏
			Motor_DifferentialControl(200,-120);
			break;
		case -4: // 极端左偏
			Motor_DifferentialControl(150,-150);
			break;
		case 4: // 极端右偏
			Motor_DifferentialControl(150,150);
			break;
		default: // 异常情况，保持上次动作
			if(last_temp != 0) {
				// 根据上次偏移方向继续调整
				if(last_temp < 0) {
					Motor_DifferentialControl(200,-100); // 继续左转
				} else {
					Motor_DifferentialControl(200,100);  // 继续右转
				}
			} else {
				Motor_DifferentialControl(200,0); // 直行
			}
			break;
	}

	// 更新历史状态
	last_gray = gray;
	last_temp = temp;
}