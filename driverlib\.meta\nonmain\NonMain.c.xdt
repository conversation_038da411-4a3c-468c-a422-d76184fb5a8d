%%{
/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== NonMain.Board.c.xdt ========
 */

    let NONMAIN = system.modules["/ti/driverlib/NONMAIN"];
    let inst = NONMAIN.$static;

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let deviceOptions = system.getScript("/ti/driverlib/nonmain/NONMAINOptions.js");
%%}
/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ boot_config.c =============
 *  Configured MSPM0 NONMAIN options
 *
 *  DO NOT EDIT - This file is generated by the SysConfig tool.
 */

#include "boot_config.h"
#include "ti_msp_dl_config.h"
%
%%{
    let debugAccessStr = "BCR_CFG_DEBUG_ACCESS_DIS";
    if (inst.debugAccessEnable == "enabled")
    {
        debugAccessStr = "BCR_CFG_DEBUG_ACCESS_EN";
    }
    else if (inst.debugAccessEnable == "enabledWithPW")
    {
        debugAccessStr = "BCR_CFG_DEBUG_ACCESS_EN_PW";
    }

    let massEraseStr = "BCR_CFG_MASS_ERASE_DIS";
    if (inst.meMode == "enabled")
    {
        massEraseStr = "BCR_CFG_MASS_ERASE_EN";
    }
    else if (inst.meMode == "enabledWithPW")
    {
        massEraseStr = "BCR_CFG_MASS_ERASE_EN_PW";
    }

    let factoryResetStr = "BCR_CFG_FACTORY_RESET_DIS";
    if (inst.frMode == "enabled")
    {
        factoryResetStr = "BCR_CFG_FACTORY_RESET_EN";
    }
    else if (inst.frMode == "enabledWithPW")
    {
        factoryResetStr = "BCR_CFG_FACTORY_RESET_EN_PW";
    }
%%}
%
%%{
function createHexStr(value)
{
    if (value == 0xFFFFFFFF)
    {
        return ("CFG_DEFAULT_VALUE");
    }

    return ("0x" + value.toString(16));
}
%%}

/* Bootcode configuration */

PLACE_IN_MEMORY(".BCRConfig")
const BCR_Config BCRConfig =
{
    .bcrConfigID          = `createHexStr(inst.bcrConfigID)`,
    .debugAccess          = `debugAccessStr`,
    .swdpMode             = `inst.swdEnable ? "BCR_CFG_SWDP_EN" : "BCR_CFG_SWDP_DIS"`,
%   if (!Common.isDeviceFamily_PARENT_MSPM0C110X()) {
    .tifaMode             = `inst.tifaEnable ? "BCR_CFG_TIFA_EN" : "BCR_CFG_TIFA_DIS"`,
    .bslPinInvokeEnable   = `inst.bslInvokePinCheck ? "BCR_CFG_BSL_PIN_INVOKE_EN" : "BCR_CFG_BSL_PIN_INVOKE_DIS"`,
%   if (deviceOptions.BCR_SUPPORT_CSC == true){
    .staticWriteProtectionMainLow  = `createHexStr(inst.staticWriteProtectionMainLow)`,
    .staticWriteProtectionMainHigh = `createHexStr(inst.staticWriteProtectionMainHigh)`,
    .staticWriteProtectionNonMain  = `inst.staticWriteProtectionNonMain ? "BCR_CFG_NON_MAIN_STATIC_PROT_EN" : "BCR_CFG_NON_MAIN_STATIC_PROT_DIS"`,
    .debugHold            = `inst.debugHold ? "BCR_CFG_DEBUG_HOLD_EN" : "BCR_CFG_DEBUG_HOLD_DIS"`,
    .CSCexist             = `inst.cscExists ? "BCR_CFG_CSC_EXIST" : "BCR_CFG_CSC_NOT_EXIST"`,
%       if (deviceOptions.BCR_SUPPORT_BANK_SWAP == true) {
    .flashBankSwapPolicy  = `inst.flashBankSwap ? "BCR_CFG_FLASH_BS_EN" : "BCR_CFG_FLASH_BS_DIS"`,
%       } else {
    .bc_reserved_0        = 0xFFFFU,
%       }
%   } // end device CSC check
%
%   if (deviceOptions.SUPPORT_PW_HASH == false) {
    .passwordDebugLock    = {`createHexStr(inst.swdPW0)`, `createHexStr(inst.swdPW1)`,
        `createHexStr(inst.swdPW2)`, `createHexStr(inst.swdPW3)`},
%   } // device does not support hashed passwords
    .fastBootMode         = `inst.fastBootMode ? "BCR_CFG_FAST_BOOT_EN" : "BCR_CFG_FAST_BOOT_DIS"`,
    .bootloaderMode       = `inst.bslMode ? "BCR_CFG_BOOTLOADER_MODE_EN" : "BCR_CFG_BOOTLOADER_MODE_DIS"`,
    .massEraseMode        = `massEraseStr`,
%   } // device is not M0C check end
    .factoryResetMode     = `factoryResetStr`,
%   if (deviceOptions.SUPPORT_PW_HASH == false) {
    .passwordMassErase    = {`createHexStr(inst.mePW0)`, `createHexStr(inst.mePW1)`,
        `createHexStr(inst.mePW2)`, `createHexStr(inst.mePW3)`},
    .passwordFactoryReset = {`createHexStr(inst.frPW0)`, `createHexStr(inst.frPW1)`,
        `createHexStr(inst.frPW2)`, `createHexStr(inst.frPW3)`},
%   } else if (deviceOptions.SUPPORT_PW_HASH == true) {
    .passwordMassErase    = {`createHexStr(inst.mePW0)`, `createHexStr(inst.mePW1)`,
        `createHexStr(inst.mePW2)`, `createHexStr(inst.mePW3)`,
        `createHexStr(inst.mePW4)`, `createHexStr(inst.mePW5)`,
        `createHexStr(inst.mePW6)`, `createHexStr(inst.mePW7)`},
    .passwordFactoryReset = {`createHexStr(inst.frPW0)`, `createHexStr(inst.frPW1)`,
        `createHexStr(inst.frPW2)`, `createHexStr(inst.frPW3)`,
        `createHexStr(inst.frPW4)`, `createHexStr(inst.frPW5)`,
        `createHexStr(inst.frPW6)`, `createHexStr(inst.frPW7)`},
    .passwordDebugLock    = {`createHexStr(inst.swdPW0)`, `createHexStr(inst.swdPW1)`,
        `createHexStr(inst.swdPW2)`, `createHexStr(inst.swdPW3)`,
        `createHexStr(inst.swdPW4)`, `createHexStr(inst.swdPW5)`,
        `createHexStr(inst.swdPW6)`, `createHexStr(inst.swdPW7)`},
%   } // device supports hashed passwords check end
%
%   /* Due to structure padding, write protection fields for M0C need to be in a different order */
%   if (deviceOptions.BCR_SUPPORT_CSC == false){
%       if (!Common.isDeviceFamily_PARENT_MSPM0C110X()) {
    .staticWriteProtectionMainLow  = `createHexStr(inst.staticWriteProtectionMainLow)`,
    .staticWriteProtectionMainHigh = `createHexStr(inst.staticWriteProtectionMainHigh)`,
    .staticWriteProtectionNonMain  = `inst.staticWriteProtectionNonMain ? "BCR_CFG_NON_MAIN_STATIC_PROT_EN" : "BCR_CFG_NON_MAIN_STATIC_PROT_DIS"`,
%       } else {
    .staticWriteProtectionNonMain  = `inst.staticWriteProtectionNonMain ? "BCR_CFG_NON_MAIN_STATIC_PROT_EN" : "BCR_CFG_NON_MAIN_STATIC_PROT_DIS"`,
    .staticWriteProtectionMainLow  = `createHexStr(inst.staticWriteProtectionMainLow)`,
    .staticWriteProtectionMainHigh = `createHexStr(inst.staticWriteProtectionMainHigh)`,
%       } // device M0C check end
%   } // device CSC check end
%
%   if (!Common.isDeviceFamily_PARENT_MSPM0C110X()){
%       if (deviceOptions.BCR_SUPPORT_BANK_SWAP == true) {
    .bc_reserved_0                 = 0xFFFFU,
%       } else {
%           if (!(Common.isDeviceFamily_PARENT_MSPM0G1X0X_G3X0X() ||
%                 Common.isDeviceFamily_PARENT_MSPM0L11XX_L13XX())) {
    .bc_reserved_1                 = 0xFFFFU,
%           }
%       } // device bank swap check end (for reserved offsets)
    .secureBootMode                = `inst.appCRCCheck ? "BCR_CFG_SECURE_BOOT_EN" : "BCR_CFG_SECURE_BOOT_DIS"`,
    .userSecureAppStartAddr        = `createHexStr(inst.appCRCCheckStartAddress)`,
    .userSecureAppLength           = `createHexStr(inst.appCRCCheckLength)`,
%   if (deviceOptions.BCR_SUPPORT_USER_APP_HASH == true) {
%   /* TODO: User app hash functionality not implemented yet. Left default vals */
    .userSecureAppHash = {CFG_DEFAULT_VALUE, CFG_DEFAULT_VALUE,
        CFG_DEFAULT_VALUE, CFG_DEFAULT_VALUE,
        CFG_DEFAULT_VALUE, CFG_DEFAULT_VALUE,
        CFG_DEFAULT_VALUE, CFG_DEFAULT_VALUE},
%       if (deviceOptions.BCR_SUPPORT_DUAL_BANK == true) {
    .staticWriteProtectionMainHigh_2 = `createHexStr(inst.staticWriteProtectionMainHigh_2)`,
%       } else {
%           if (deviceOptions.BCR_SUPPORT_BANK_SWAP == true) {
    .bc_reserved_1                 = 0xFFFFFFFFU,
%           } else {
    .bc_reserved_2                 = 0xFFFFFFFFU,
%           } // device bank swap check end (for reserved offsets)
%       } // device dual bank check end
%   } else {
    .userSecureAppCrc              = `createHexStr(inst.appCRC)`,
%   } // device user app hash check end
    .userCfgCRC                    = `createHexStr(inst.bootCRC)`,
%   } else {
    .reserved                      = 0xFFFFFFFFU,
%   } // device is M0C check end
};

%   if (deviceOptions.SUPPORT_BSL == true) {
%%{
    let pluginTypeStr = "BSL_CFG_PLUGIN_TYPE_ANY";
    if (deviceOptions.BSL_SUPPORT_FLASH_PLUGIN == true)
    {
        if (inst.bslPluginType == "bslPluginUART")
        {
            pluginTypeStr = "BSL_CFG_PLUGIN_TYPE_UART";
        }
        else if (inst.bslPluginType == "bslPluginI2C")
        {
            pluginTypeStr = "BSL_CFG_PLUGIN_TYPE_I2C";
        }
    }

    let securityAlertStr = "BSL_CFG_SECURITY_IGNORE";
    if (deviceOptions.SUPPORT_ROM_BSL == true)
    {
        if (inst.bslSecurityConfig == "triggerFactoryReset")
        {
            securityAlertStr = "BSL_CFG_SECURITY_FACTORY_RESET";
        }
        else if (inst.bslSecurityConfig == "disableBSL")
        {
            securityAlertStr = "BSL_CFG_SECURITY_DISABLE_BSL";
        }
    }
%%}

/* Bootloader configuration */

PLACE_IN_MEMORY(".BSLConfig")
const BSL_Config BSLConfig =
{
    .configID                          = `createHexStr(inst.bslConfigID)`,
    .interfacePins.UART_RXD_pad_num    = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_UART_RXD_PAD" : "0xFFU"`,
    .interfacePins.UART_RXD_PF_mux_sel = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_UART_RXD_MUX" : "0xFFU"`,
    .interfacePins.UART_TXD_pad_num    = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_UART_TXD_PAD" : "0xFFU"`,
    .interfacePins.UART_TXD_PF_mux_sel = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_UART_TXD_MUX" : "0xFFU"`,
    .interfacePins.I2C_SDA_pad_num     = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_I2C_SDA_PAD" : "0xFFU"`,
    .interfacePins.I2C_SDA_PF_mux_sel  = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_I2C_SDA_MUX" : "0xFFU"`,
    .interfacePins.I2C_SCL_pad_num     = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_I2C_SCL_PAD" : "0xFFU"`,
    .interfacePins.I2C_SCL_PF_mux_sel  = `(deviceOptions.SUPPORT_ROM_BSL == true) ? "DEF_I2C_SCL_MUX" : "0xFFU"`,
    .pin.pinData0                      = DEFAULT_BSL_PIN_INVOCATION_DATA0,
    .pin.pinData1                      = DEFAULT_BSL_PIN_INVOCATION_DATA1,
%   if (deviceOptions.SUPPORT_ROM_BSL == false) {
    .memoryRead         = 0xFFFFU,
    .password           = {0xFFFFFFFFU, 0xFFFFFFFFU,
        0xFFFFFFFFU, 0xFFFFFFFFU, 0xFFFFFFFFU,
        0xFFFFFFFFU, 0xFFFFFFFFU, 0xFFFFFFFFU},
%   } else {
    .memoryRead         = `inst.bslEnableReadOut ? "BSL_CFG_MEMORY_READOUT_ENABLE" : "BSL_CFG_MEMORY_READOUT_DISABLE"`,
    .password           = {`createHexStr(inst.bslPW0)`, `createHexStr(inst.bslPW1)`,
        `createHexStr(inst.bslPW2)`, `createHexStr(inst.bslPW3)`, `createHexStr(inst.bslPW4)`,
        `createHexStr(inst.bslPW5)`, `createHexStr(inst.bslPW6)`, `createHexStr(inst.bslPW7)`},
%   } // device support Flash BSL check end
%   if (deviceOptions.BSL_SUPPORT_FLASH_PLUGIN == true) {
    .pluginType         = `pluginTypeStr`,
    .flashPluginEnable  = `inst.bslFlashPluginEnable ? "BSL_CFG_PLUGIN_FLASH_EXIST" : "BSL_CFG_PLUGIN_FLASH_NOT_EXIST"`,
    .pluginSramSize     = `createHexStr(inst.bslPluginSRAMSize)`,
    .pluginHook[0]      = `(inst.bslPluginHookInit.length) ? "(uint32_t) " + inst.bslPluginHookInit : "CFG_DEFAULT_VALUE"`,
    .pluginHook[1]      = `(inst.bslPluginHookReceive.length) ? "(uint32_t) " + inst.bslPluginHookReceive : "CFG_DEFAULT_VALUE"`,
    .pluginHook[2]      = `(inst.bslPluginHookTransmit.length) ? "(uint32_t) " + inst.bslPluginHookTransmit : "CFG_DEFAULT_VALUE"`,
    .pluginHook[3]      = `(inst.bslPluginHookDeInit.length) ? "(uint32_t) " + inst.bslPluginHookDeInit : "CFG_DEFAULT_VALUE"`,
%   } else {
    .bl_reserved_0[0]   = 0xFFFFFFFFU,
    .bl_reserved_0[1]   = 0xFFFFFFFFU,
    .bl_reserved_0[2]   = 0xFFFFFFFFU,
    .bl_reserved_0[3]   = 0xFFFFFFFFU,
    .bl_reserved_0[4]   = 0xFFFFFFFFU,
%   } // device support flash plugin check end
%   if (deviceOptions.BSL_SUPPORT_UART_BAUD == true) {
%       if (deviceOptions.SUPPORT_ROM_BSL == true) {
    .uartBaudRate       = DEF_UART_BAUD_RATE,
%       } else {
    .uartBaudRate       = 0xFFFFU,
%       }
%   }
    .BSLAlternateConfig = `inst.bslAltConfig ? "BSL_CFG_FLASH_BSL_EXIST" : "BSL_CFG_FLASH_BSL_NOT_EXIST"`,
%   if (deviceOptions.BSL_SUPPORT_UART_BAUD == false){
    .reserved           = 0xFFFFU,
%   }
    .BSLAlternateAddr   = `createHexStr(inst.bslAltAddress)`,
%   if (deviceOptions.SUPPORT_ROM_BSL == false) {
    .appRev             = (uint32_t *) 0xFFFFFFFFU,
    .securityAlert      = 0xFFFFU,
    .i2cSlaveAddress    = 0xFFFFU,
%   } else {
    .appRev             = `"(uint32_t *) " + createHexStr(inst.bslAppVersion)`,
    .securityAlert      = `securityAlertStr`,
    .i2cSlaveAddress    = `createHexStr(inst.i2cSlaveAddress)`,
%   }
%   if ((deviceOptions.BSL_DISABLE_NRST == true) ||
%       (deviceOptions.SUPPORT_ROM_BSL == false)) {
%       if (deviceOptions.SUPPORT_ROM_BSL == true) {
    .disableNRST        = `inst.disableNRST ? "BSL_CFG_NRST_DISABLE" : "BSL_CFG_NRST_ENABLE"`,
%       } else if (deviceOptions.SUPPORT_ROM_BSL == false) {
    .disableNRST        = 0xFFFFU,
%       }
%       if (deviceOptions.BSL_SUPPORT_FLASH_PLUGIN == true) {
    .bl_reserved_0      = 0xFFFFU,
    .bl_reserved_1      = 0xFFFFFFFFU,
%       } else {
    .bl_reserved_1      = 0xFFFFU,
    .bl_reserved_2      = 0xFFFFFFFFU,
%       }
%   }
    .userCfgCRC         = `createHexStr(inst.bslCRC)`,
};

/* Added for secondary_bsl to build */
%   } // device supports BSL check end
/* 'main' function will never get called */
#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
__attribute__((weak)) int main(void)
{
    return (0);
}
#elif defined(__IAR_SYSTEMS_ICC__)
__weak int main(void);
#elif (defined(__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)) || defined(__GNUC__)
__attribute__((weak)) int main(void);
#else
#error "Non-Main table currently not supported for this compiler"
#endif
