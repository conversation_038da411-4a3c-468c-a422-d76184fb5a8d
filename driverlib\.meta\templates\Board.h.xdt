/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

%%{
/*
 *  ======== ti_msp_dl_config.h ========
 */

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    /* localize the object names with this device ID */
    let devId = system.deviceData.deviceId;
    let part = system.deviceData.part;
    let boardName = Common.boardName();
%%}
/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the `boardName`
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_`boardName`
#define CONFIG_`system.deviceData.gpn`

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */

%%{
    let keys = [];
    let board = system.modules["/ti/driverlib/Board"].$static;
    if(board){
        let priority = 0;
        let proposedKey = board["InitPriority"+priority];
        while(proposedKey !== undefined){
            if(system.modules[proposedKey])
            {
                keys.push(proposedKey);
            }
            priority++;
            proposedKey = board["InitPriority"+priority];
        }
    }
%%}

/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)

% /* loop over all remaining modules in the current configuration */
% for (let i = 0; i < keys.length; i++) {
%   let mod = system.modules[keys[i]];

%   if (mod.templates && mod.templates.boardh) {
        % /* load its template and expand it */
        %    let gen = system.getTemplate(mod.templates.boardh);
`gen(mod,"Define")`
    % }
% }
% /* Special Case TIMERFault */
% if(system.modules["/ti/driverlib/TIMERFault"]){
%    let faultMod = system.modules["/ti/driverlib/TIMERFault"];
%       if (faultMod.templates && faultMod.templates.boardh) {
%           /* load its template and expand it */
%           let gen = system.getTemplate(faultMod.templates.boardh);
`gen(faultMod,"Define")`
    % }
% }
/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
% if(!board.debugOn){
void SYSCFG_DL_DEBUG_init(void);
%}
% /* loop over all remaining modules in the current configuration */
% for (let i = 0; i < keys.length; i++) {
%   let mod = system.modules[keys[i]];
%   if (mod.templates && mod.templates.boardh) {
        % /* load its template and expand it */
        %    let gen = system.getTemplate(mod.templates.boardh);
`_.trimEnd(gen(mod,"Declare"))`
    % }
% }

%%{
/* Retention: check if available for peripheral  */
let retentionCount = 0;
if(Common.isDeviceM0G()){
    for (let i = 0; i < keys.length; i++) {
        let mod = system.modules[keys[i]];
        /* Retention template */
        if (mod.templates && mod.templates.boardc && mod.templates.Retention) {
            retentionCount++;
        }
    }
}
%%}
% if(retentionCount>0){
bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);
% }

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
