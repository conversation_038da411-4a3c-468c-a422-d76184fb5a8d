#include "ti_msp_dl_config.h"
#include "task.h"

uint8_t task_num;
void Oled_Proc(void);
static Task_Struct MyTask[]={

{0,50,50,Sensor_Test},   // ��ʹ�ô�������Ժ���
{0,10,10,Key_Process}    // ��������������10ms����
};


void Task_Init(void)
{
	task_num=sizeof(MyTask)/sizeof(Task_Struct);
}

void Task_Marks(void)
{
	unsigned char i;
	for(i=0;i<task_num;i++)
	{
		MyTask[i].Time_Cnt_Down--;
		if(MyTask[i].Time_Cnt_Down==0)
		{
			MyTask[i].Runing_Flag=1;
			MyTask[i].Time_Cnt_Down=MyTask[i].Time_Init_Num;
		}
	}
}

void Task_Proc(void)
{
	unsigned char i;
	for(i=0;i<task_num;i++)
	{
		if(MyTask[i].Runing_Flag==1)
		{
			MyTask[i].TaskHander();
			MyTask[i].Runing_Flag=0;
		}
	}
}


