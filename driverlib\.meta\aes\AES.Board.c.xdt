%%{
/*
 * Copyright (c) 2018-2019, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CO<PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== AES.Board.c.xdt ========
 */
    /* args[] passed by /ti/drivers/templates/Board.c.xdt during function call: */
    let AES = args[0];
    let content = args[1];

    /* get Common /ti/driverlib utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    /* shorthand names for some common references in template below */
    let stat = AES.$static;
    if (stat.length == 0) return;

/*
 * Standard Trampoline:
 * In order to preserve spacing, it is important to also set the boolean
 * values in the templates object based on whether that condition should
 * produce any code
 * Example:
 * templates: {
 *       boardc: "/ti/driverlib/comp/COMP.Board.c.xdt",
 *       boardh: "/ti/driverlib/comp/COMP.Board.h.xdt",
 *       Call: true,
 *       Reset: false,
 *       Power: true,
 *       GPIO: false,
 *       Function: true
 * },
 */

    switch(content){
        case "Call":
            printCall();
            break;
        case "Reset":
            printReset();
            break;
        case "Power":
            printPower();
            break;
        case "GPIO":
            printGPIO();
            break;
        case "Function":
            printFunction();
            break;
        case "RetentionDeclare":
            printRetentionDeclare();
            break;
        case "RetentionSave":
            printRetentionSave();
            break;
        case "RetentionRestore":
            printRetentionRestore();
            break;
        case "RetentionRdy":
            printRetentionRdy();
            break;
        default:
            /* do nothing */
            break;
    }
%%}
%
% function printCall() {
    SYSCFG_DL_AES_init();
% }
%
% /* Retention Configuration Code */
% function printRetentionDeclare(){
`Common.getStaticRetentionDeclareC(stat,"AES")`
% }
% function printRetentionRdy(){
`Common.getStaticRetentionRdyC(stat,"AES")`
% }
% function printRetentionSave(){
`Common.getStaticRetentionSaveC(stat,"AES")`
% }
% function printRetentionRestore(){
`Common.getStaticRetentionRestoreC(stat,"AES")`
% }
%
% function printReset() {
    DL_AES_reset(AES);
% }
%
% function printPower() {
    DL_AES_enablePower(AES);
% }
%
% function printFunction() {
SYSCONFIG_WEAK void SYSCFG_DL_AES_init(void)
{
    DL_AES_init(AES, DL_AES_MODE_`stat.operationType`_`stat.blockCipherMode`, `stat.keyLength`);
% /* DMA Interrupt Configuration */
%   if(stat.aesConfigureDMA0 && stat.aesDMATrigger0){
    DL_AES_enableDMATrigger0Interrupt(AES);
%   }
%   if(stat.aesConfigureDMA1 && stat.aesDMATrigger1){
    DL_AES_enableDMATrigger1Interrupt(AES);
%   }
%   if(stat.aesConfigureDMA2 && stat.aesDMATrigger2){
    DL_AES_enableDMATrigger2Interrupt(AES);
%   }
%
%   if (stat.enableInterrupt) {

    DL_AES_clearInterruptStatus(AES);
    DL_AES_enableInterrupt(AES);
%        if(stat.interruptPriority !== "DEFAULT"){
    NVIC_SetPriority(AES_INT_IRQn, `stat.interruptPriority`);
%        }
%   }
}
% }
