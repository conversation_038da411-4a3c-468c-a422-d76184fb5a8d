%%{
/*
 * Copyright (c) 2018 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== TIMERFault.Board.h.xdt ========
 */

    let mod = args[0]; /* passed by /ti/driverlib/templates/Board.c.xdt */
    let content = args[1];

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let instances = mod.$instances;
    if(instances.length == 0) return;

    switch(content) {
        case "Define":
            printDefine();
            break;
        case "Declare":
            printDeclare();
            break;
        default:
            /* do nothing */
            return;
    }

%%}
%
%%{
// TODO: work on updating the names of the variables as defined in the board.c file to match the ones generated here
%%}
% function printDefine() {
%%{
    for(let i in instances){
        let inst = instances[i];
%%}
%           if ((typeof inst["faultPin0"] !== 'undefined')){
%               let gpioStr = "GPIO_"+inst.$name;
%               let faultPinName = "fault"+"Pin0";
%               let faultPackagePin = inst[faultPinName].$solution.packagePinName;
%               let faultPinCM = Common.getAttribute((system.deviceData.devicePins[faultPackagePin]),("iomux_pincm"));
%               let faultGpioName = system.deviceData.devicePins[faultPackagePin].mux.muxSetting.find(item => item["mode"] === "1").peripheralPin.peripheralName;
%               let faultPort = Common.getGPIOPort(faultGpioName);
%               let faultGpioPin = Common.getGPIONumber(faultGpioName);
%               let faultPortStr = "#define "+gpioStr+"_FAULT_PIN_0_PORT";
`faultPortStr.padEnd(40," ")+faultPort.padStart(40, " ")`
%               let faultPinStr = "#define "+gpioStr+"_FAULT_PIN_0";
%               let faultPinStr2 = "DL_GPIO_PIN_"+faultGpioPin;
`faultPinStr.padEnd(40," ")+faultPinStr2.padStart(40," ")`
%               let faultIOmuxStr = "#define "+gpioStr+"_IOMUX_FAULT_PIN_0";
%               let faultIOmuxStr2 = "(IOMUX_PINCM"+faultPinCM+")";
`faultIOmuxStr.padEnd(40," ")+faultIOmuxStr2.padStart(40," ")`
%               let faultFuncStr = "#define "+gpioStr+"_IOMUX_FAULT_PIN_0_FUNC";
%               let faultFuncStr2 = "IOMUX_PINCM"+faultPinCM+"_PF_"+"TIMA"+"_FAULT0";
`faultFuncStr.padEnd(40, " ")+faultFuncStr2.padStart(40, " ")`
%           } // if typeof
%           if ((typeof inst["faultPin1"] !== 'undefined')){
%               let gpioStr = "GPIO_"+inst.$name;
%               let faultPinName = "fault"+"Pin1";
%               let faultPackagePin = inst[faultPinName].$solution.packagePinName;
%               let faultPinCM = Common.getAttribute((system.deviceData.devicePins[faultPackagePin]),("iomux_pincm"));
%               let faultGpioName = system.deviceData.devicePins[faultPackagePin].mux.muxSetting.find(item => item["mode"] === "1").peripheralPin.peripheralName;
%               let faultPort = Common.getGPIOPort(faultGpioName);
%               let faultGpioPin = Common.getGPIONumber(faultGpioName);
%               let faultPortStr = "#define "+gpioStr+"_FAULT_PIN_1_PORT";
`faultPortStr.padEnd(40," ")+faultPort.padStart(40, " ")`
%               let faultPinStr = "#define "+gpioStr+"_FAULT_PIN_1";
%               let faultPinStr2 = "DL_GPIO_PIN_"+faultGpioPin;
`faultPinStr.padEnd(40," ")+faultPinStr2.padStart(40," ")`
%               let faultIOmuxStr = "#define "+gpioStr+"_IOMUX_FAULT_PIN_1";
%               let faultIOmuxStr2 = "(IOMUX_PINCM"+faultPinCM+")";
`faultIOmuxStr.padEnd(40," ")+faultIOmuxStr2.padStart(40," ")`
%               let faultFuncStr = "#define "+gpioStr+"_IOMUX_FAULT_PIN_1_FUNC";
%               let faultFuncStr2 = "IOMUX_PINCM"+faultPinCM+"_PF_"+"TIMA"+"_FAULT1";
`faultFuncStr.padEnd(40, " ")+faultFuncStr2.padStart(40, " ")`
%           } // if typeof
%           if ((typeof inst["faultPin2"] !== 'undefined')){
%               let gpioStr = "GPIO_"+inst.$name;
%               let faultPinName = "fault"+"Pin2";
%               let faultPackagePin = inst[faultPinName].$solution.packagePinName;
%               let faultPinCM = Common.getAttribute((system.deviceData.devicePins[faultPackagePin]),("iomux_pincm"));
%               let faultGpioName = system.deviceData.devicePins[faultPackagePin].mux.muxSetting.find(item => item["mode"] === "1").peripheralPin.peripheralName;
%               let faultPort = Common.getGPIOPort(faultGpioName);
%               let faultGpioPin = Common.getGPIONumber(faultGpioName);
%               let faultPortStr = "#define "+gpioStr+"_FAULT_PIN_2_PORT";
`faultPortStr.padEnd(40," ")+faultPort.padStart(40, " ")`
%               let faultPinStr = "#define "+gpioStr+"_FAULT_PIN_2";
%               let faultPinStr2 = "DL_GPIO_PIN_"+faultGpioPin;
`faultPinStr.padEnd(40," ")+faultPinStr2.padStart(40," ")`
%               let faultIOmuxStr = "#define "+gpioStr+"_IOMUX_FAULT_PIN_2";
%               let faultIOmuxStr2 = "(IOMUX_PINCM"+faultPinCM+")";
`faultIOmuxStr.padEnd(40," ")+faultIOmuxStr2.padStart(40," ")`
%               let faultFuncStr = "#define "+gpioStr+"_IOMUX_FAULT_PIN_2_FUNC";
%               let faultFuncStr2 = "IOMUX_PINCM"+faultPinCM+"_PF_"+"TIMA"+"_FAULT2";
`faultFuncStr.padEnd(40, " ")+faultFuncStr2.padStart(40, " ")`
%           } // if typeof
%           } //for(let i in instances)
% } // end of function printDefine
%
% function printDeclare() {
% // do nothing, GPIO already part of defines
% return;
% }
