#include "ti_msp_dl_config.h"
#include "Motor.h"
#include "Gray.h"

uint8_t Read_GraySensors(void)
{
    uint8_t sensor_data = 0;

    // ��ȡ6·�������������Ҷ�Ӧbit0-bit5
    // ʹ����������: ���⵽����ʱ���͵�ƽ(0)������ʱ���ߵ�ƽ(1)
    if (!DL_GPIO_readPins(GPIO_GRAY_PIN_1_PORT, GPIO_GRAY_PIN_1_PIN)) sensor_data |= 0x01; // ����� - ���⵽����ʱΪ1
    if (!DL_GPIO_readPins(GPIO_GRAY_PIN_2_PORT, GPIO_GRAY_PIN_2_PIN)) sensor_data |= 0x02; //
    if (!DL_GPIO_readPins(GPIO_GRAY_PIN_3_PORT, GPIO_GRAY_PIN_3_PIN)) sensor_data |= 0x04; //
    if (!DL_GPIO_readPins(GPIO_GRAY_PIN_4_PORT, GPIO_GRAY_PIN_4_PIN)) sensor_data |= 0x08; // ����
    if (!DL_GPIO_readPins(GPIO_GRAY_PIN_5_PORT, GPIO_GRAY_PIN_5_PIN)) sensor_data |= 0x10; //
    if (!DL_GPIO_readPins(GPIO_GRAY_PIN_6_PORT, GPIO_GRAY_PIN_6_PIN)) sensor_data |= 0x20; // ���Ҳ�

    return sensor_data;
}

int8_t Calculate_LinePosition(uint8_t sensor_data)
{
    // �����⵽�ߵĴ���������
    uint8_t sensor_count = 0;
    for(int i = 0; i < 6; i++) {
        if(sensor_data & (1 << i)) sensor_count++;
    }

    // ���������ݣ�����0��ʾδ���⵽��
    if(sensor_count == 0) {
        return 99; // ����ֵ��ʾδ���⵽��
    }

    // ����������� - ����Ѱ��
    if(sensor_count == 1) {
        switch (sensor_data) {
            case 0b000001: return -3; // ������1������������
            case 0b000010: return -2; // ������2
            case 0b000100: return -1; // ������3
            case 0b001000: return 0;  // ������4��������
            case 0b010000: return 1;  // ������5
            case 0b100000: return 2;  // ������6�������Ҳ�
            default: return 0;
        }
    }

    // �ഫ������� - ����㴦��
    else if(sensor_count >= 2) {
        // �ж�����ջ����ҹ�
        uint8_t left_sensors = sensor_data & 0b000111;  // ������1-2-3
        uint8_t right_sensors = sensor_data & 0b111000; // ������4-5-6

        if(left_sensors && !right_sensors) {
            return -4; // �����㣬��Ҫ�����ת
        }
        else if(right_sensors && !left_sensors) {
            return 4;  // �ҹ���㣬��Ҫ�����ת
        }
        else if(left_sensors && right_sensors) {
            return 0;  // T��·�ڻ�ʮ��·�ڣ�����ֱ��
        }
    }

    // Ĭ��ֱ��
    return 0;
}