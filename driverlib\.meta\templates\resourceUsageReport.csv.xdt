%%{
    // Basic Template Setup / Go through modules
    /* get common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    /* localize the object names with the device ID */
    let devId = system.deviceData.deviceId;
    let part = system.deviceData.part;
    let boardName = Common.boardName();

    /* sort all modules in the current config by initPriority */
    let modules = [];
    let max = 0;
    for (let moduleName in system.modules) {
        var mod = system.modules[moduleName];
        if (max <= mod.initPriority) {
            max = mod.initPriority + 1;
        }
    }

    let board = system.modules["/ti/driverlib/Board"].$static;
    let keys = Common.getModuleKeys();

%%}
% if(board.genResourceCSVAdvanced == "brief"){
Pin Number,Device Pin Name,IO Type,Assigned Peripheral,Assigned Function ,User Label
% } else if(board.genResourceCSVAdvanced == "detailed"){
Pin Number,Device Pin Name,IO Type,Assigned Peripheral,Assigned Function ,User Label, All Pin Functions
% }
% if(board.genResourceCSV){
%%{
    let readUsed = {};
    let readUsedPeripherals = {};

    let allPins = system.deviceData.devicePins;
    /* Special Case of GPIO module pins, get used pins */
    if(keys.includes("/ti/driverlib/GPIO")){
        /* Initialize array of current instance pins*/
        let readGPIO = [];
        /* Iterate through current instance pins */
        if(system.modules["/ti/driverlib/gpio/GPIOPin"].$instances){
            for(let currentInstance of system.modules["/ti/driverlib/gpio/GPIOPin"].$instances){

                // Extract pin with resource name for per-instance pin list */
                let pinWithResource = "";
                let pinInternalResistor = "";
                if(currentInstance.internalResistor !== "NONE"){
                    pinInternalResistor = " with internal " + currentInstance.internalResistor.toLowerCase().replace("_","-");
                }
                pinWithResource += currentInstance.direction.charAt(0) + currentInstance.direction.slice(1).toLowerCase()+pinInternalResistor+": "
                pinWithResource += currentInstance.pin.$solution.devicePinName;
                let pinIOType = Common.getAttribute((system.deviceData.gpio.pinInfo[currentInstance.pin.$solution.packagePinName].devicePin),("io_type"));
                let pinIOTypeExtended = "";
                switch (pinIOType) {
                    case "SD":
                        pinIOTypeExtended = "Standard";
                        break;
                    case "SDW":
                        pinIOTypeExtended = "Standard with Wake";
                        break;
                    case "HS":
                        pinIOTypeExtended = "High-Speed";
                        break;
                    case "HD":
                        pinIOTypeExtended = "High-Drive";
                        break;
                    case "OD":
                        pinIOTypeExtended = "Open-Drain";
                        break;
                    case "SDL":
                        pinIOTypeExtended = "Standard";
                        break;
                    case "USB":
                        pinIOTypeExtended = "Standard";
                        break;
                    default:
                        break;
                }

                let pinSolution = currentInstance.pin.$solution;
                let resource_pinNumber = pinSolution.packagePinName;
                let resource_devicePinName = pinSolution.devicePinName;
                let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                let resource_assignedFunction = pinIOTypeExtended + " " + currentInstance.direction.charAt(0) + currentInstance.direction.slice(1).toLowerCase()+pinInternalResistor;
                let resource_assignedPeripheral = "GPIO";
                let resource_userLabel = currentInstance.$name;
                let resource_otherUses = "";
                try{
                    let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                    resource_otherUses = "\""+allUsesList.join(", ")+"\""
                }catch (e) {
                    // do nothing
                }

                let pinWithIOType = pinIOTypeExtended + " " + pinWithResource;
                let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                if(board.genResourceCSVAdvanced == "detailed"){
                    resourceString += ","+resource_otherUses
                }
                // readGPIO.push(pinWithIOType);
                readGPIO.push(resourceString)
            }
            /* Organize GPIO pins per group */
            let readPortA = (readGPIO.filter(e => e.includes("PA")));
            let readPortB = (readGPIO.filter(e => e.includes("PB")));
            if(readPortA.length>0){
                readUsed["GPIOA"] = readPortA;
            }
            if(readPortB.length>0){
                readUsed["GPIOB"] = readPortB;
            }
        }
    }
    /* Special Case of TimerFault module, get used pins */
    if(Common.hasTimerA()){
        try{
            if(system.modules["/ti/driverlib/TIMERFault"].$instances){
                for(let currentInstance of system.modules["/ti/driverlib/TIMERFault"].$instances){
                    /* Extract Current Instance resource name */
                    let readCurrentName = "";
                    try{
                        readCurrentName = "TIMER FAULT "+system.modules["/ti/driverlib/TIMERFault"].$instances.indexOf(currentInstance);
                    }catch (e) {
                        // do nothing
                    }
                    /* Initialize array of current instance pins*/
                    readUsed[readCurrentName] = [];
                    /* Iterate through current instance pins */
                    if(system.modules["/ti/driverlib/TIMERFault"].pinmuxRequirements){
                        if(system.modules["/ti/driverlib/TIMERFault"].pinmuxRequirements(currentInstance)){
                            for(let currentResource of system.modules["/ti/driverlib/TIMERFault"].pinmuxRequirements(currentInstance)){
                                // Extract pin with resource name for per-instance pin list */
                                if(readCurrentName.length>0){

                                    /* ======================= PIN CODE ======================= */
                                    let pinSolution = currentInstance[currentResource.name].$solution;
                                    let resource_pinNumber = pinSolution.packagePinName;
                                    let resource_devicePinName = pinSolution.devicePinName;
                                    let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                                    let resource_assignedFunction = "\""+currentResource.displayName+"\"";
                                    let resource_assignedPeripheral = "TIMER";
                                    let resource_userLabel = ""
                                    let resource_otherUses = "";
                                    try{
                                        let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                                        resource_otherUses = "\""+allUsesList.join(", ")+"\""
                                    }catch (e) {
                                        // do nothing
                                    }

                                    let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                                    if(board.genResourceCSVAdvanced == "detailed"){
                                        resourceString += ","+resource_otherUses
                                    }
                                    /* ======================= PIN CODE ======================= */
                                    readUsed[readCurrentName].push(resourceString);
                                }
                            }
                        }
                    }
                }
            }
        }catch (e) {
            // do nothing
        }
    }

    for(let currentModule of keys){
        if(currentModule == "/ti/driverlib/GPIO"){
            // do nothing, taken care of in outside case
        }
        /* Special Case: Tamper IO pin filtering - only for MSPM0L122X_L222X family */
        else if(currentModule == "/ti/driverlib/TAMPERIO"){
            try{
                /* Extract Current Instance resource name */
                let readCurrentName = "TAMPER IO";
                /* Initialize array of current instance pins*/
                readUsed[readCurrentName] = [];
                /* Iterate through current instance pins */
                if(system.modules[currentModule].moduleStatic.pinmuxRequirements){
                    if(system.modules[currentModule].moduleStatic.pinmuxRequirements(system.modules[currentModule].$static)){
                        for(let currentResource of system.modules[currentModule].moduleStatic.pinmuxRequirements(system.modules[currentModule].$static)){
                            /* Condition: skip the peripheral resource for text generation */
                            if(currentResource.name !== "peripheral"){
                                /* ======================= PIN CODE ======================= */
                                let pinSolution = system.modules[currentModule].$static[currentResource.name].$solution;
                                let resource_pinNumber = pinSolution.packagePinName;
                                let resource_devicePinName = pinSolution.devicePinName;
                                let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                                let resource_assignedFunction = "\""+currentResource.displayName+"\"";
                                let resource_assignedPeripheral = "TAMPER IO"; // TODO: see if name update needed
                                let resource_userLabel = ""
                                let resource_otherUses = "";
                                try{
                                    let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                                    resource_otherUses = "\""+allUsesList.join(", ")+"\""
                                }catch (e) {
                                    // do nothing
                                }

                                let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                                if(board.genResourceCSVAdvanced == "detailed"){
                                    resourceString += ","+resource_otherUses
                                }
                                /* ======================= PIN CODE ======================= */
                                readUsed[readCurrentName].push(resourceString);
                            }
                        }
                    }
                }
            }catch (e) {
                // do nothing
            }
        }
        /* Get used pins from static modules */
        else if(system.modules[currentModule].moduleStatic){
            /* Extract Current Instance resource name */
            let readCurrentName = "";
            try{
                readCurrentName = system.modules[currentModule].displayName;
            }catch (e) {
                // do nothing
            }
            /* Initialize array of current instance pins*/
            readUsed[readCurrentName] = [];
            /* Iterate through current instance pins */
            if(system.modules[currentModule].moduleStatic.pinmuxRequirements){
                if(system.modules[currentModule].moduleStatic.pinmuxRequirements(system.modules[currentModule].$static)[0]){
                    for(let currentResource of system.modules[currentModule].moduleStatic.pinmuxRequirements(system.modules[currentModule].$static)[0].resources){
                        // Extract pin with resource name for per-instance pin list */
                        if(readCurrentName.length>0){

                            /* ======================= PIN CODE ======================= */
                            let pinSolution = system.modules[currentModule].$static.peripheral[currentResource.name].$solution;
                            let resource_pinNumber = pinSolution.packagePinName;
                            let resource_devicePinName = pinSolution.devicePinName;
                            let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                            let resource_assignedFunction = "\""+currentResource.displayName+"\"";
                            let resource_assignedPeripheral = readCurrentName;
                            let resource_userLabel = ""
                            let resource_otherUses = "";
                            try{
                                let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                                resource_otherUses = "\""+allUsesList.join(", ")+"\""
                            }catch (e) {
                                // do nothing
                            }

                            let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                            if(board.genResourceCSVAdvanced == "detailed"){
                                resourceString += ","+resource_otherUses
                            }
                            /* ======================= PIN CODE ======================= */
                            readUsed[readCurrentName].push(resourceString);
                        }

                    }
                }
            }
        }
        /* Get used pins from non-static modules */
        else if(system.modules[currentModule].$instances){
            if(system.modules[currentModule].displayName.includes("TIMER")){
                // do nothing - case handled inside instances loop
            }
            else{
                readUsedPeripherals[system.modules[currentModule].displayName] = system.modules[currentModule].$instances.length+" out of "+system.modules[currentModule].maxInstances+" instances ("+parseInt(system.modules[currentModule].$instances.length/system.modules[currentModule].maxInstances*100)+"%)"
            }
            for(let currentInstance of system.modules[currentModule].$instances){
                /* Get TIMER Channel Resources */
                if(system.modules[currentModule].displayName.includes("TIMER")){
                    let timerPeripheral = currentInstance.peripheral.$solution.peripheralName;
                    let ccIndexOptionsLength = ((Common.isTimerFourCCCapable(currentInstance)) ? 4:2);
                    if(currentModule == "/ti/driverlib/TIMER"){

                    }
                    else if(currentModule == "/ti/driverlib/PWM"){
                        readUsedPeripherals[timerPeripheral] = currentInstance.ccIndex.length+" channels used out of "+ccIndexOptionsLength+" ("+parseInt(currentInstance.ccIndex.length/ccIndexOptionsLength*100)+"%)"
                    }
                    else if(currentModule == "/ti/driverlib/CAPTURE"){
                        if(currentInstance.captureInputMode == "multi"){
                            readUsedPeripherals[timerPeripheral] = currentInstance.ccIndexMulti.length+" channels used out of "+ccIndexOptionsLength+" ("+parseInt(currentInstance.ccIndexMulti.length/ccIndexOptionsLength*100)+"%)"
                        }
                        else{
                            readUsedPeripherals[timerPeripheral] = "1"+" channels used out of "+ccIndexOptionsLength+" ("+parseInt(1/ccIndexOptionsLength*100)+"%)"
                        }
                    }
                    else if(currentModule == "/ti/driverlib/COMPARE"){
                        if(!Common.isInternalTimerChannel(currentInstance.ccIndex)){
                            readUsedPeripherals[timerPeripheral] = "1"+" channels used out of "+ccIndexOptionsLength+" ("+parseInt(1/ccIndexOptionsLength*100)+"%)"
                        }

                    }
                    else if(currentModule == "/ti/driverlib/QEI"){
                        readUsedPeripherals[timerPeripheral] = "2"+" channels used out of "+ccIndexOptionsLength+" ("+parseInt(2/ccIndexOptionsLength*100)+"%)"
                    }
                }
                /* Extract Current Instance resource name */
                let readCurrentName = "";
                try{
                    readCurrentName = currentInstance.peripheral.$solution.peripheralName;
                }catch (e) {
                    // do nothing
                }
                /* Initialize array of current instance pins*/
                readUsed[readCurrentName] = [];
                /* Iterate through current instance pins */
                if(system.modules[currentModule].pinmuxRequirements){
                    if(system.modules[currentModule].pinmuxRequirements(currentInstance)[0]){
                        for(let currentResource of system.modules[currentModule].pinmuxRequirements(currentInstance)[0].resources){
                            // Extract pin with resource name for per-instance pin list */
                            if(readCurrentName.length>0){
                                /* ======================= PIN CODE ======================= */
                                let pinSolution = currentInstance.peripheral[currentResource.name].$solution;
                                let resource_pinNumber = pinSolution.packagePinName;
                                let resource_devicePinName = pinSolution.devicePinName;
                                let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                                let resource_assignedFunction = "\""+currentResource.displayName+"\"";
                                let resource_assignedPeripheral = readCurrentName;
                                let resource_userLabel = "";
                                let resource_otherUses = "";
                                try{
                                    let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                                    resource_otherUses = "\""+allUsesList.join(", ")+"\""
                                }catch (e) {
                                    // do nothing
                                }

                                let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                                if(board.genResourceCSVAdvanced == "detailed"){
                                    resourceString += ","+resource_otherUses
                                }
                                /* ======================= PIN CODE ======================= */
                                readUsed[readCurrentName].push(resourceString);
                            }
                        }
                    }
                }
            }
        }
    }
    /* Special Case of Board module, get used pins */
    if(system.modules["/ti/driverlib/Board"].moduleStatic){

        /* Initialize array of current instance pins*/
        readUsed["BOARD"] = [];
        /* Iterate through current instance pins */
        for(let currentResource of system.modules["/ti/driverlib/Board"].moduleStatic.pinmuxRequirements(system.modules["/ti/driverlib/Board"].$static)[0].resources){

            /* ======================= PIN CODE ======================= */
            let resource_pinNumber = system.modules["/ti/driverlib/Board"].$static.peripheral[currentResource.name].$solution.packagePinName;
            let resource_devicePinName = system.modules["/ti/driverlib/Board"].$static.peripheral[currentResource.name].$solution.devicePinName;
            let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[system.modules["/ti/driverlib/Board"].$static.peripheral[currentResource.name].$solution.packagePinName].devicePin),("io_type"));
            let resource_assignedFunction = "\""+currentResource.displayName+"\"";
            let resource_assignedPeripheral = "BOARD";
            let resource_userLabel = "";
            let resource_otherUses = "";
            try{
                let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                resource_otherUses = "\""+allUsesList.join(", ")+"\""
            }catch (e) {
                // do nothing
            }

            let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
            if(board.genResourceCSVAdvanced == "detailed"){
                resourceString += ","+resource_otherUses
            }
            /* ======================= PIN CODE ======================= */
            readUsed["BOARD"].push(resourceString);
        }
    }
    /* Special Case of Clock Tree Pins */
    if(system.modules["/ti/driverlib/SYSCTL"]){
        if(system.modules["/ti/driverlib/SYSCTL"].moduleStatic){
            /* Initialize array of current instance pins*/
            if(!readUsed["SYSCTL"]){ readUsed["SYSCTL"] = []};
            // Clock Tree enabled
            if(system.modules["/ti/driverlib/SYSCTL"].$static.clockTreeEn){

                /* Get used pins from Clock Tree pinFunction */
                if(system.modules["/ti/clockTree/pinFunction.js"].$instances){
                    for(let currentInstance of system.modules["/ti/clockTree/pinFunction.js"].$instances){
                        if(system.modules["/ti/clockTree/pinFunction.js"].pinmuxRequirements){
                            if(system.modules["/ti/clockTree/pinFunction.js"].pinmuxRequirements(currentInstance)[0]){
                                for(let currentResource of system.modules["/ti/clockTree/pinFunction.js"].pinmuxRequirements(currentInstance)[0].resources){
                                    /* ======================= PIN CODE ======================= */
                                    let pinSolution = currentInstance.peripheral[currentResource.name].$solution;
                                    let resource_pinNumber = pinSolution.packagePinName;
                                    let resource_devicePinName = pinSolution.devicePinName;
                                    let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                                    let resource_assignedFunction = "\""+currentResource.displayName+"\"";
                                    let resource_assignedPeripheral = "SYSCTL";
                                    let resource_userLabel = "";
                                    let resource_otherUses = "";
                                    try{
                                        let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                                        resource_otherUses = "\""+allUsesList.join(", ")+"\""
                                    }catch (e) {
                                        // do nothing
                                    }

                                    let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                                    if(board.genResourceCSVAdvanced == "detailed"){
                                        resourceString += ","+resource_otherUses
                                    }
                                    /* ======================= PIN CODE ======================= */
                                    readUsed["SYSCTL"].push(resourceString);
                                }
                            }
                        }
                    }
                }

                /* Get used pins from Clock Tree oscillator */
                if(system.modules["/ti/clockTree/oscillator.js"].$instances){
                    for(let currentInstance of system.modules["/ti/clockTree/oscillator.js"].$instances){
                        if(system.modules["/ti/clockTree/oscillator.js"].pinmuxRequirements){
                            if(system.modules["/ti/clockTree/oscillator.js"].pinmuxRequirements(currentInstance)[0]){
                                for(let currentResource of system.modules["/ti/clockTree/oscillator.js"].pinmuxRequirements(currentInstance)[0].resources){
                                    /* ======================= PIN CODE ======================= */
                                    let pinSolution = currentInstance.peripheral[currentResource.name].$solution;
                                    let resource_pinNumber = pinSolution.packagePinName;
                                    let resource_devicePinName = pinSolution.devicePinName;
                                    let resource_IOType = Common.getAttribute((system.deviceData.gpio.pinInfo[pinSolution.packagePinName].devicePin),("io_type"));
                                    let resource_assignedFunction = "\""+currentResource.displayName+"\"";
                                    let resource_assignedPeripheral = "SYSCTL";
                                    let resource_userLabel = "";
                                    let resource_otherUses = "";
                                    try{
                                        let allUsesList = (system.deviceData.devicePins[resource_pinNumber].mux.muxSetting).map(a => a.peripheralPin.name);
                                        resource_otherUses = "\""+allUsesList.join(", ")+"\""
                                    }catch (e) {
                                        // do nothing
                                    }

                                    let resourceString = resource_pinNumber+","+resource_devicePinName+","+resource_IOType+","+resource_assignedPeripheral+","+resource_assignedFunction+","+resource_userLabel
                                    if(board.genResourceCSVAdvanced == "detailed"){
                                        resourceString += ","+resource_otherUses
                                    }
                                    /* ======================= PIN CODE ======================= */
                                    readUsed["SYSCTL"].push(resourceString);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    let readUsedPrint = "";
    let resourceSummaryPeripherals = "";
    for(var key in readUsedPeripherals){
        resourceSummaryPeripherals += key+","+readUsedPeripherals[key]+"\n"
    }
    for(var key in readUsed){
        // Add peripheral instance name
        var nameToAdd = ("\n- "+key.toString());
        // Format: Check string not empty, add pins.
        if (/[A-Za-z]+/.test(nameToAdd)){
            // readUsedPrint += nameToAdd;
            if(readUsed[key].length > 0)
            for(var pin of readUsed[key]){
                // readUsedPrint += ("\n\t+ "+pin+"");
                if(readUsedPrint.length !== 0){
                    readUsedPrint += "\n";
                }
                readUsedPrint += (pin+"");
            }
        }
    }
%%}
`readUsedPrint`


Peripheral,Usage
`resourceSummaryPeripherals`
% } // end if(board.genResourcesCSV)
