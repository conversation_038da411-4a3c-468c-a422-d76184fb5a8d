/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                              = "GPIO_LED";
GPIO1.associatedPins[0].$name            = "PIN_LED";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].assignedPort     = "PORTB";
GPIO1.associatedPins[0].pin.$assign      = "PB22";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "GPIO_GRAY";
GPIO2.associatedPins.create(6);
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[0].$name            = "PIN_2";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[1].$name            = "PIN_1";
GPIO2.associatedPins[2].direction        = "INPUT";
GPIO2.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[2].$name            = "PIN_3";
GPIO2.associatedPins[3].direction        = "INPUT";
GPIO2.associatedPins[3].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[3].$name            = "PIN_4";
GPIO2.associatedPins[4].direction        = "INPUT";
GPIO2.associatedPins[4].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[4].$name            = "PIN_5";
GPIO2.associatedPins[5].direction        = "INPUT";
GPIO2.associatedPins[5].$name            = "PIN_6";

GPIO3.$name                              = "GPIO_KEY";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name            = "PIN_KEY3";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].internalResistor = "PULL_UP";
GPIO3.associatedPins[0].pin.$assign      = "PB12";
GPIO3.associatedPins[1].$name            = "PIN_KEY4";
GPIO3.associatedPins[1].direction        = "INPUT";
GPIO3.associatedPins[1].pin.$assign      = "PB13";

GPIO4.$name                              = "GPIO_STEP";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name            = "DIR1";
GPIO4.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[0].pin.$assign      = "PA17";
GPIO4.associatedPins[1].$name            = "DIR2";
GPIO4.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[1].pin.$assign      = "PA16";

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicControllerBusSpeed           = 400000;
I2C1.intController                     = ["NACK","RXFIFO_TRIGGER","RX_DONE","TX_DONE"];
I2C1.peripheral.$assign                = "I2C0";
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";

PWM1.$name                      = "PWM_MOTOR1";
PWM1.clockPrescale              = 32;
PWM1.timerStartTimer            = true;
PWM1.ccIndex                    = [2];
PWM1.peripheral.$assign         = "TIMA0";
PWM1.peripheral.ccp2Pin.$assign = "PB4";
PWM1.ccp2PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM1.PWM_CHANNEL_2.$name        = "ti_driverlib_pwm_PWMTimerCC0";

PWM2.$name                              = "PWM_MOTOR2";
PWM2.ccIndex                            = [1];
PWM2.clockPrescale                      = 32;
PWM2.timerStartTimer                    = true;
PWM2.peripheral.$assign                 = "TIMA1";
PWM2.peripheral.ccp1Pin.$assign         = "PB5";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.dutyCycle            = 50;

SYSCTL.forceDefaultClkConfig = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 32000;
SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "0";
SYSTICK.systickEnable     = true;

UART1.$name                             = "UART_WIT";
UART1.targetBaudRate                    = 115200;
UART1.direction                         = "RX";
UART1.enableFIFO                        = true;
UART1.rxTimeoutValue                    = 1;
UART1.enabledInterrupts                 = ["RX_TIMEOUT_ERROR"];
UART1.enabledDMARXTriggers              = "DL_UART_DMA_INTERRUPT_RX";
UART1.peripheral.rxPin.$assign          = "PA9";
UART1.rxPinConfig.hideOutputInversion   = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor  = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType  = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                 = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.DMA_CHANNEL_RX.addressMode        = "f2b";
UART1.DMA_CHANNEL_RX.srcLength          = "BYTE";
UART1.DMA_CHANNEL_RX.dstLength          = "BYTE";
UART1.DMA_CHANNEL_RX.enableInterrupt    = true;
UART1.DMA_CHANNEL_RX.$name              = "DMA_WIT";
UART1.DMA_CHANNEL_RX.peripheral.$assign = "DMA_CH0";

UART2.$name                            = "UART_0";
UART2.targetBaudRate                   = 115200;
UART2.peripheral.$assign               = "UART0";
UART2.peripheral.rxPin.$assign         = "PA11";
UART2.peripheral.txPin.$assign         = "PA10";
UART2.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART2.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB14";
GPIO2.associatedPins[1].pin.$suggestSolution = "PB15";
GPIO2.associatedPins[2].pin.$suggestSolution = "PB16";
GPIO2.associatedPins[3].pin.$suggestSolution = "PA12";
GPIO2.associatedPins[4].pin.$suggestSolution = "PA13";
GPIO2.associatedPins[5].pin.$suggestSolution = "PA14";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";
UART1.peripheral.$suggestSolution            = "UART1";
