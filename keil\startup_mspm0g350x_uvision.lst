


ARM Macro Assembler    Page 1 


    1 00000000         ;//*****************************************************
                       ************************
    2 00000000         ;//
    3 00000000         ;// Copyright (C) 2022 Texas Instruments Incorporated - 
                       http://www.ti.com/
    4 00000000         ;//
    5 00000000         ;// Redistribution and use in source and binary forms, w
                       ith or without
    6 00000000         ;// modification, are permitted provided that the follow
                       ing conditions
    7 00000000         ;// are met:
    8 00000000         ;//
    9 00000000         ;//  Redistributions of source code must retain the abov
                       e copyright
   10 00000000         ;//  notice, this list of conditions and the following d
                       isclaimer.
   11 00000000         ;//
   12 00000000         ;//  Redistributions in binary form must reproduce the a
                       bove copyright
   13 00000000         ;//  notice, this list of conditions and the following d
                       isclaimer in the
   14 00000000         ;//  documentation and/or other materials provided with 
                       the
   15 00000000         ;//  distribution.
   16 00000000         ;//
   17 00000000         ;//  Neither the name of Texas Instruments Incorporated 
                       nor the names of
   18 00000000         ;//  its contributors may be used to endorse or promote 
                       products derived
   19 00000000         ;//  from this software without specific prior written p
                       ermission.
   20 00000000         ;//
   21 00000000         ;// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS A
                       ND CONTRIBUTORS
   22 00000000         ;// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLU
                       DING, BUT NOT
   23 00000000         ;// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILIT
                       Y AND FITNESS FOR
   24 00000000         ;// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHA
                       LL THE COPYRIGHT
   25 00000000         ;// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDI
                       RECT, INCIDENTAL,
   26 00000000         ;// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUD
                       ING, BUT NOT
   27 00000000         ;// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVI
                       CES; LOSS OF USE,
   28 00000000         ;// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER 
                       CAUSED AND ON ANY
   29 00000000         ;// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIA
                       BILITY, OR TORT
   30 00000000         ;// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY W
                       AY OUT OF THE USE
   31 00000000         ;// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
                        OF SUCH DAMAGE.
   32 00000000         ;//
   33 00000000         ;// MSPM0G3507 startup file
   34 00000000         ;//
   35 00000000         ;//*****************************************************
                       ***********************



ARM Macro Assembler    Page 2 


   36 00000000         ;//-------- <<< Use Configuration Wizard in Context Menu
                        >>> ------------------
   37 00000000         ;*/
   38 00000000         
   39 00000000         ; <h> Stack Configuration
   40 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   41 00000000         ; </h>
   42 00000000         
   43 00000000 00000100 
                       Stack_Size
                               EQU              0x00000100
   44 00000000         
   45 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   46 00000000         Stack_Mem
                               SPACE            Stack_Size
   47 00000100         __initial_sp
   48 00000100         
   49 00000100         
   50 00000100         ; <h> Heap Configuration
   51 00000100         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   52 00000100         ; </h>
   53 00000100         
   54 00000100 00000000 
                       Heap_Size
                               EQU              0x00000000
   55 00000100         
   56 00000100                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   57 00000000         __heap_base
   58 00000000         Heap_Mem
                               SPACE            Heap_Size
   59 00000000         __heap_limit
   60 00000000         
   61 00000000         
   62 00000000                 PRESERVE8
   63 00000000                 THUMB
   64 00000000         
   65 00000000         
   66 00000000         ; Vector Table Mapped to Address 0 at Reset
   67 00000000         
   68 00000000                 AREA             RESET, DATA, READONLY
   69 00000000                 EXPORT           __Vectors
   70 00000000                 EXPORT           __Vectors_End
   71 00000000                 EXPORT           __Vectors_Size
   72 00000000         
   73 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   74 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   75 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   76 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   77 00000010 00000000        DCD              0           ; Reserved
   78 00000014 00000000        DCD              0           ; Reserved
   79 00000018 00000000        DCD              0           ; Reserved
   80 0000001C 00000000        DCD              0           ; Reserved
   81 00000020 00000000        DCD              0           ; Reserved
   82 00000024 00000000        DCD              0           ; Reserved



ARM Macro Assembler    Page 3 


   83 00000028 00000000        DCD              0           ; Reserved
   84 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   85 00000030 00000000        DCD              0           ; Reserved
   86 00000034 00000000        DCD              0           ; Reserved
   87 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   88 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   89 00000040         
   90 00000040         ; External Interrupts
   91 00000040 00000000        DCD              GROUP0_IRQHandler ;  1:  GROUP0
                                                             interrupt handler
   92 00000044 00000000        DCD              GROUP1_IRQHandler ;  2:  GROUP1
                                                             interrupt handler
   93 00000048 00000000        DCD              TIMG8_IRQHandler ;  3:  TIMG8 i
                                                            nterrupt handler
   94 0000004C 00000000        DCD              UART3_IRQHandler ;  4:  UART3 i
                                                            nterrupt handler
   95 00000050 00000000        DCD              ADC0_IRQHandler ;  5:  ADC0 int
                                                            errupt handler
   96 00000054 00000000        DCD              ADC1_IRQHandler ;  6:  ADC1 int
                                                            errupt handler
   97 00000058 00000000        DCD              CANFD0_IRQHandler ;  7:  CANFD0
                                                             interrupt handler
   98 0000005C 00000000        DCD              DAC0_IRQHandler ;  8:  DAC0 int
                                                            errupt handler
   99 00000060 00000000        DCD              0           ;  9:  Reserved
  100 00000064 00000000        DCD              SPI0_IRQHandler ; 10:  SPI0 int
                                                            errupt handler
  101 00000068 00000000        DCD              SPI1_IRQHandler ; 11:  SPI1 int
                                                            errupt handler
  102 0000006C 00000000        DCD              0           ; 12:  Reserved
  103 00000070 00000000        DCD              0           ; 13:  Reserved
  104 00000074 00000000        DCD              UART1_IRQHandler ; 14:  UART1 i
                                                            nterrupt handler
  105 00000078 00000000        DCD              UART2_IRQHandler ; 15:  UART2 i
                                                            nterrupt handler
  106 0000007C 00000000        DCD              UART0_IRQHandler ; 16:  UART0 i
                                                            nterrupt handler
  107 00000080 00000000        DCD              TIMG0_IRQHandler ; 17:  TIMG0 i
                                                            nterrupt handler
  108 00000084 00000000        DCD              TIMG6_IRQHandler ; 18:  TIMG6 i
                                                            nterrupt handler
  109 00000088 00000000        DCD              TIMA0_IRQHandler ; 19:  TIMA0 i
                                                            nterrupt handler
  110 0000008C 00000000        DCD              TIMA1_IRQHandler ; 20:  TIMA1 i
                                                            nterrupt handler
  111 00000090 00000000        DCD              TIMG7_IRQHandler ; 21:  TIMG7 i
                                                            nterrupt handler
  112 00000094 00000000        DCD              TIMG12_IRQHandler ; 22:  TIMG12
                                                             interrupt handler
  113 00000098 00000000        DCD              0           ; 23:  Reserved
  114 0000009C 00000000        DCD              0           ; 24:  Reserved
  115 000000A0 00000000        DCD              I2C0_IRQHandler ; 25:  I2C0 int
                                                            errupt handler
  116 000000A4 00000000        DCD              I2C1_IRQHandler ; 26:  I2C1 int
                                                            errupt handler
  117 000000A8 00000000        DCD              0           ; 27:  Reserved
  118 000000AC 00000000        DCD              0           ; 28:  Reserved



ARM Macro Assembler    Page 4 


  119 000000B0 00000000        DCD              AES_IRQHandler ; 29:  AES inter
                                                            rupt handler
  120 000000B4 00000000        DCD              0           ; 30:  Reserved
  121 000000B8 00000000        DCD              RTC_IRQHandler ; 31:  RTC inter
                                                            rupt handler
  122 000000BC 00000000        DCD              DMA_IRQHandler ; 32:  DMA inter
                                                            rupt handler
  123 000000C0         
  124 000000C0         __Vectors_End
  125 000000C0         
  126 000000C0 000000C0 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  127 000000C0         
  128 000000C0                 AREA             |.text|, CODE, READONLY
  129 00000000         
  130 00000000         
  131 00000000         ; Reset Handler
  132 00000000         
  133 00000000         Reset_Handler
                               PROC
  134 00000000                 EXPORT           Reset_Handler             [WEAK
]
  135 00000000         ;IMPORT  SystemInit
  136 00000000                 IMPORT           __main
  137 00000000         ; SystemInit can be called here, but not necessary for M
                       SPM0
  138 00000000         ;LDR     R0, =SystemInit
  139 00000000         ;BLX     R0
  140 00000000 4806            LDR              R0, =__main
  141 00000002 4700            BX               R0
  142 00000004                 ENDP
  143 00000004         
  144 00000004         
  145 00000004         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  146 00000004         
  147 00000004         NMI_Handler
                               PROC
  148 00000004                 EXPORT           NMI_Handler               [WEAK
]
  149 00000004 E7FE            B                .
  150 00000006                 ENDP
  152 00000006         HardFault_Handler
                               PROC
  153 00000006                 EXPORT           HardFault_Handler         [WEAK
]
  154 00000006 E7FE            B                .
  155 00000008                 ENDP
  156 00000008         SVC_Handler
                               PROC
  157 00000008                 EXPORT           SVC_Handler               [WEAK
]
  158 00000008 E7FE            B                .
  159 0000000A                 ENDP
  160 0000000A         PendSV_Handler
                               PROC
  161 0000000A                 EXPORT           PendSV_Handler            [WEAK
]



ARM Macro Assembler    Page 5 


  162 0000000A E7FE            B                .
  163 0000000C                 ENDP
  164 0000000C         SysTick_Handler
                               PROC
  165 0000000C                 EXPORT           SysTick_Handler           [WEAK
]
  166 0000000C E7FE            B                .
  167 0000000E                 ENDP
  168 0000000E         
  169 0000000E         Default_Handler
                               PROC
  170 0000000E                 EXPORT           Default_Handler           [WEAK
]
  171 0000000E                 EXPORT           GROUP0_IRQHandler         [WEAK
]
  172 0000000E                 EXPORT           GROUP1_IRQHandler         [WEAK
]
  173 0000000E                 EXPORT           TIMG8_IRQHandler          [WEAK
]
  174 0000000E                 EXPORT           UART3_IRQHandler          [WEAK
]
  175 0000000E                 EXPORT           ADC0_IRQHandler           [WEAK
]
  176 0000000E                 EXPORT           ADC1_IRQHandler           [WEAK
]
  177 0000000E                 EXPORT           CANFD0_IRQHandler         [WEAK
]
  178 0000000E                 EXPORT           DAC0_IRQHandler           [WEAK
]
  179 0000000E                 EXPORT           SPI0_IRQHandler           [WEAK
]
  180 0000000E                 EXPORT           SPI1_IRQHandler           [WEAK
]
  181 0000000E                 EXPORT           UART1_IRQHandler          [WEAK
]
  182 0000000E                 EXPORT           UART2_IRQHandler          [WEAK
]
  183 0000000E                 EXPORT           UART0_IRQHandler          [WEAK
]
  184 0000000E                 EXPORT           TIMG0_IRQHandler          [WEAK
]
  185 0000000E                 EXPORT           TIMG6_IRQHandler          [WEAK
]
  186 0000000E                 EXPORT           TIMA0_IRQHandler          [WEAK
]
  187 0000000E                 EXPORT           TIMA1_IRQHandler          [WEAK
]
  188 0000000E                 EXPORT           TIMG7_IRQHandler          [WEAK
]
  189 0000000E                 EXPORT           TIMG12_IRQHandler         [WEAK
]
  190 0000000E                 EXPORT           I2C0_IRQHandler           [WEAK
]
  191 0000000E                 EXPORT           I2C1_IRQHandler           [WEAK
]
  192 0000000E                 EXPORT           AES_IRQHandler            [WEAK
]
  193 0000000E                 EXPORT           RTC_IRQHandler            [WEAK
]



ARM Macro Assembler    Page 6 


  194 0000000E                 EXPORT           DMA_IRQHandler            [WEAK
]
  195 0000000E         
  196 0000000E         GROUP0_IRQHandler
  197 0000000E         GROUP1_IRQHandler
  198 0000000E         TIMG8_IRQHandler
  199 0000000E         UART3_IRQHandler
  200 0000000E         ADC0_IRQHandler
  201 0000000E         ADC1_IRQHandler
  202 0000000E         CANFD0_IRQHandler
  203 0000000E         DAC0_IRQHandler
  204 0000000E         SPI0_IRQHandler
  205 0000000E         SPI1_IRQHandler
  206 0000000E         UART1_IRQHandler
  207 0000000E         UART2_IRQHandler
  208 0000000E         UART0_IRQHandler
  209 0000000E         TIMG0_IRQHandler
  210 0000000E         TIMG6_IRQHandler
  211 0000000E         TIMA0_IRQHandler
  212 0000000E         TIMA1_IRQHandler
  213 0000000E         TIMG7_IRQHandler
  214 0000000E         TIMG12_IRQHandler
  215 0000000E         I2C0_IRQHandler
  216 0000000E         I2C1_IRQHandler
  217 0000000E         AES_IRQHandler
  218 0000000E         RTC_IRQHandler
  219 0000000E         DMA_IRQHandler
  220 0000000E E7FE            B                .
  221 00000010                 ENDP
  222 00000010         
  223 00000010                 ALIGN
  224 00000010         
  225 00000010         
  226 00000010         ; User Initial Stack & Heap
  227 00000010         
  228 00000010                 IF               :DEF:__MICROLIB
  235 00000010         
  236 00000010                 IMPORT           __use_two_region_memory
  237 00000010                 EXPORT           __user_initial_stackheap
  238 00000010         
  239 00000010         __user_initial_stackheap
                               PROC
  240 00000010 4803            LDR              R0, =  Heap_Mem
  241 00000012 4904            LDR              R1, =(Stack_Mem + Stack_Size)
  242 00000014 4A02            LDR              R2, = (Heap_Mem +  Heap_Size)
  243 00000016 4B04            LDR              R3, = Stack_Mem
  244 00000018 4770            BX               LR
  245 0000001A                 ENDP
  246 0000001A         
  247 0000001A 00 00           ALIGN
  248 0000001C         
  249 0000001C                 ENDIF
  250 0000001C         
  251 0000001C         
  252 0000001C                 END
              00000000 
              00000000 
              00000100 
              00000000 



ARM Macro Assembler    Page 7 


Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=.\objects\startup_mspm0g350x_uvision.d -o.\objects\startup_mspm0g
350x_uvision.o --predefine="__UVISION_VERSION SETA 534" --predefine="__MSPM0G35
07__ SETA 1" --list=.\startup_mspm0g350x_uvision.lst startup_mspm0g350x_uvision
.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 45 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 46 in file startup_mspm0g350x_uvision.s
   Uses
      At line 241 in file startup_mspm0g350x_uvision.s
      At line 243 in file startup_mspm0g350x_uvision.s

__initial_sp 00000100

Symbol: __initial_sp
   Definitions
      At line 47 in file startup_mspm0g350x_uvision.s
   Uses
      At line 73 in file startup_mspm0g350x_uvision.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 56 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 58 in file startup_mspm0g350x_uvision.s
   Uses
      At line 240 in file startup_mspm0g350x_uvision.s
      At line 242 in file startup_mspm0g350x_uvision.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 57 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000000

Symbol: __heap_limit
   Definitions
      At line 59 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 68 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 73 in file startup_mspm0g350x_uvision.s
   Uses
      At line 69 in file startup_mspm0g350x_uvision.s
      At line 126 in file startup_mspm0g350x_uvision.s

__Vectors_End 000000C0

Symbol: __Vectors_End
   Definitions
      At line 124 in file startup_mspm0g350x_uvision.s
   Uses
      At line 70 in file startup_mspm0g350x_uvision.s
      At line 126 in file startup_mspm0g350x_uvision.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 128 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: .text unused
ADC0_IRQHandler 0000000E

Symbol: ADC0_IRQHandler
   Definitions
      At line 200 in file startup_mspm0g350x_uvision.s
   Uses
      At line 95 in file startup_mspm0g350x_uvision.s
      At line 175 in file startup_mspm0g350x_uvision.s

ADC1_IRQHandler 0000000E

Symbol: ADC1_IRQHandler
   Definitions
      At line 201 in file startup_mspm0g350x_uvision.s
   Uses
      At line 96 in file startup_mspm0g350x_uvision.s
      At line 176 in file startup_mspm0g350x_uvision.s

AES_IRQHandler 0000000E

Symbol: AES_IRQHandler
   Definitions
      At line 217 in file startup_mspm0g350x_uvision.s
   Uses
      At line 119 in file startup_mspm0g350x_uvision.s
      At line 192 in file startup_mspm0g350x_uvision.s

CANFD0_IRQHandler 0000000E

Symbol: CANFD0_IRQHandler
   Definitions
      At line 202 in file startup_mspm0g350x_uvision.s
   Uses
      At line 97 in file startup_mspm0g350x_uvision.s
      At line 177 in file startup_mspm0g350x_uvision.s

DAC0_IRQHandler 0000000E

Symbol: DAC0_IRQHandler
   Definitions
      At line 203 in file startup_mspm0g350x_uvision.s
   Uses
      At line 98 in file startup_mspm0g350x_uvision.s
      At line 178 in file startup_mspm0g350x_uvision.s

DMA_IRQHandler 0000000E

Symbol: DMA_IRQHandler
   Definitions
      At line 219 in file startup_mspm0g350x_uvision.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 122 in file startup_mspm0g350x_uvision.s
      At line 194 in file startup_mspm0g350x_uvision.s

Default_Handler 0000000E

Symbol: Default_Handler
   Definitions
      At line 169 in file startup_mspm0g350x_uvision.s
   Uses
      At line 170 in file startup_mspm0g350x_uvision.s
Comment: Default_Handler used once
GROUP0_IRQHandler 0000000E

Symbol: GROUP0_IRQHandler
   Definitions
      At line 196 in file startup_mspm0g350x_uvision.s
   Uses
      At line 91 in file startup_mspm0g350x_uvision.s
      At line 171 in file startup_mspm0g350x_uvision.s

GROUP1_IRQHandler 0000000E

Symbol: GROUP1_IRQHandler
   Definitions
      At line 197 in file startup_mspm0g350x_uvision.s
   Uses
      At line 92 in file startup_mspm0g350x_uvision.s
      At line 172 in file startup_mspm0g350x_uvision.s

HardFault_Handler 00000006

Symbol: HardFault_Handler
   Definitions
      At line 152 in file startup_mspm0g350x_uvision.s
   Uses
      At line 76 in file startup_mspm0g350x_uvision.s
      At line 153 in file startup_mspm0g350x_uvision.s

I2C0_IRQHandler 0000000E

Symbol: I2C0_IRQHandler
   Definitions
      At line 215 in file startup_mspm0g350x_uvision.s
   Uses
      At line 115 in file startup_mspm0g350x_uvision.s
      At line 190 in file startup_mspm0g350x_uvision.s

I2C1_IRQHandler 0000000E

Symbol: I2C1_IRQHandler
   Definitions
      At line 216 in file startup_mspm0g350x_uvision.s
   Uses
      At line 116 in file startup_mspm0g350x_uvision.s
      At line 191 in file startup_mspm0g350x_uvision.s

NMI_Handler 00000004

Symbol: NMI_Handler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 147 in file startup_mspm0g350x_uvision.s
   Uses
      At line 75 in file startup_mspm0g350x_uvision.s
      At line 148 in file startup_mspm0g350x_uvision.s

PendSV_Handler 0000000A

Symbol: PendSV_Handler
   Definitions
      At line 160 in file startup_mspm0g350x_uvision.s
   Uses
      At line 87 in file startup_mspm0g350x_uvision.s
      At line 161 in file startup_mspm0g350x_uvision.s

RTC_IRQHandler 0000000E

Symbol: RTC_IRQHandler
   Definitions
      At line 218 in file startup_mspm0g350x_uvision.s
   Uses
      At line 121 in file startup_mspm0g350x_uvision.s
      At line 193 in file startup_mspm0g350x_uvision.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 133 in file startup_mspm0g350x_uvision.s
   Uses
      At line 74 in file startup_mspm0g350x_uvision.s
      At line 134 in file startup_mspm0g350x_uvision.s

SPI0_IRQHandler 0000000E

Symbol: SPI0_IRQHandler
   Definitions
      At line 204 in file startup_mspm0g350x_uvision.s
   Uses
      At line 100 in file startup_mspm0g350x_uvision.s
      At line 179 in file startup_mspm0g350x_uvision.s

SPI1_IRQHandler 0000000E

Symbol: SPI1_IRQHandler
   Definitions
      At line 205 in file startup_mspm0g350x_uvision.s
   Uses
      At line 101 in file startup_mspm0g350x_uvision.s
      At line 180 in file startup_mspm0g350x_uvision.s

SVC_Handler 00000008

Symbol: SVC_Handler
   Definitions
      At line 156 in file startup_mspm0g350x_uvision.s
   Uses
      At line 84 in file startup_mspm0g350x_uvision.s
      At line 157 in file startup_mspm0g350x_uvision.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


SysTick_Handler 0000000C

Symbol: SysTick_Handler
   Definitions
      At line 164 in file startup_mspm0g350x_uvision.s
   Uses
      At line 88 in file startup_mspm0g350x_uvision.s
      At line 165 in file startup_mspm0g350x_uvision.s

TIMA0_IRQHandler 0000000E

Symbol: TIMA0_IRQHandler
   Definitions
      At line 211 in file startup_mspm0g350x_uvision.s
   Uses
      At line 109 in file startup_mspm0g350x_uvision.s
      At line 186 in file startup_mspm0g350x_uvision.s

TIMA1_IRQHandler 0000000E

Symbol: TIMA1_IRQHandler
   Definitions
      At line 212 in file startup_mspm0g350x_uvision.s
   Uses
      At line 110 in file startup_mspm0g350x_uvision.s
      At line 187 in file startup_mspm0g350x_uvision.s

TIMG0_IRQHandler 0000000E

Symbol: TIMG0_IRQHandler
   Definitions
      At line 209 in file startup_mspm0g350x_uvision.s
   Uses
      At line 107 in file startup_mspm0g350x_uvision.s
      At line 184 in file startup_mspm0g350x_uvision.s

TIMG12_IRQHandler 0000000E

Symbol: TIMG12_IRQHandler
   Definitions
      At line 214 in file startup_mspm0g350x_uvision.s
   Uses
      At line 112 in file startup_mspm0g350x_uvision.s
      At line 189 in file startup_mspm0g350x_uvision.s

TIMG6_IRQHandler 0000000E

Symbol: TIMG6_IRQHandler
   Definitions
      At line 210 in file startup_mspm0g350x_uvision.s
   Uses
      At line 108 in file startup_mspm0g350x_uvision.s
      At line 185 in file startup_mspm0g350x_uvision.s

TIMG7_IRQHandler 0000000E

Symbol: TIMG7_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 213 in file startup_mspm0g350x_uvision.s
   Uses
      At line 111 in file startup_mspm0g350x_uvision.s
      At line 188 in file startup_mspm0g350x_uvision.s

TIMG8_IRQHandler 0000000E

Symbol: TIMG8_IRQHandler
   Definitions
      At line 198 in file startup_mspm0g350x_uvision.s
   Uses
      At line 93 in file startup_mspm0g350x_uvision.s
      At line 173 in file startup_mspm0g350x_uvision.s

UART0_IRQHandler 0000000E

Symbol: UART0_IRQHandler
   Definitions
      At line 208 in file startup_mspm0g350x_uvision.s
   Uses
      At line 106 in file startup_mspm0g350x_uvision.s
      At line 183 in file startup_mspm0g350x_uvision.s

UART1_IRQHandler 0000000E

Symbol: UART1_IRQHandler
   Definitions
      At line 206 in file startup_mspm0g350x_uvision.s
   Uses
      At line 104 in file startup_mspm0g350x_uvision.s
      At line 181 in file startup_mspm0g350x_uvision.s

UART2_IRQHandler 0000000E

Symbol: UART2_IRQHandler
   Definitions
      At line 207 in file startup_mspm0g350x_uvision.s
   Uses
      At line 105 in file startup_mspm0g350x_uvision.s
      At line 182 in file startup_mspm0g350x_uvision.s

UART3_IRQHandler 0000000E

Symbol: UART3_IRQHandler
   Definitions
      At line 199 in file startup_mspm0g350x_uvision.s
   Uses
      At line 94 in file startup_mspm0g350x_uvision.s
      At line 174 in file startup_mspm0g350x_uvision.s

__user_initial_stackheap 00000010

Symbol: __user_initial_stackheap
   Definitions
      At line 239 in file startup_mspm0g350x_uvision.s
   Uses
      At line 237 in file startup_mspm0g350x_uvision.s
Comment: __user_initial_stackheap used once
33 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000000

Symbol: Heap_Size
   Definitions
      At line 54 in file startup_mspm0g350x_uvision.s
   Uses
      At line 58 in file startup_mspm0g350x_uvision.s
      At line 242 in file startup_mspm0g350x_uvision.s

Stack_Size 00000100

Symbol: Stack_Size
   Definitions
      At line 43 in file startup_mspm0g350x_uvision.s
   Uses
      At line 46 in file startup_mspm0g350x_uvision.s
      At line 241 in file startup_mspm0g350x_uvision.s

__Vectors_Size 000000C0

Symbol: __Vectors_Size
   Definitions
      At line 126 in file startup_mspm0g350x_uvision.s
   Uses
      At line 71 in file startup_mspm0g350x_uvision.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

__main 00000000

Symbol: __main
   Definitions
      At line 136 in file startup_mspm0g350x_uvision.s
   Uses
      At line 140 in file startup_mspm0g350x_uvision.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 236 in file startup_mspm0g350x_uvision.s
   Uses
      None
Comment: __use_two_region_memory unused
2 symbols
384 symbols in table
