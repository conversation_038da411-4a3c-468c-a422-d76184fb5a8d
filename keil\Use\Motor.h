#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "ti_msp_dl_config.h"



void Motor_Proc(void);
static void setMotorSpeed(GPTIMER_Regs *timer, uint32_t speed_hz, DL_TIMER_CC_INDEX ccIndex);
void Motor1_SetSpeed(uint32_t speed_hz);
void Motor2_SetSpeed(uint32_t speed_hz);
void Motor_DifferentialControl(uint32_t base_speed, int16_t turn_value);
void Motor_Move(uint32_t speed, int8_t direction);
void Motor_Stop(void);
void Motor_Debug(void); // ���Ժ���
void Sensor_Test(void); // ��������Ժ���
#endif
