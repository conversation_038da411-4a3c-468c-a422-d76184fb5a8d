let Common = system.getScript("/ti/driverlib/Common.js");

let blankingSources = {
    MSPM0G1X0X_G3X0X: {
        1: {
            peripheral: "TIMA0",
            channel: "2",
        },
        2: {
            peripheral: "TIMA0",
            channel: "3",
        },
        3: {
            peripheral: "TIMA1",
            channel: "1",
        },
        4: {
            peripheral: "TIMG12",
            channel: "1",
        },
        5: {
            peripheral: "TIMG6",
            channel: "1",
        },
        6: {
            peripheral: "TIMG7",
            channel: "1",
        },
    },
    MSPM0L11XX_L13XX: {
        0: {
            peripheral: "TIMG0",
            channel: "1",
        },
        1: {
            peripheral: "TIMG1",
            channel: "1",
        },
        2: {
            peripheral: "TIMG2",
            channel: "1",
        },
    },
    MSPM0L122X_L222X: {
        1: {
            peripheral: "TIMA0",
            channel: "2",
        },
        2: {
            peripheral: "TIMA0",
            channel: "3",
        },
        3: {
            peripheral: "TIMG12",
            channel: "1",
        },
        4: {
            peripheral: "TIMG0",
            channel: "1",
        },
        5: {
            peripheral: "TIMG4",
            channel: "1",
        },
        6: {
            peripheral: "TIMG5",
            channel: "1",
        },
    },
    MSPM0L111X: {
        // N/A: COMP not available for this device
    },
    MSPM0C110X: {
        // N/A: COMP not available for this device
    },
    MSPM0GX51X: {
        1: {
            peripheral: "TIMA0",
            channel: "2",
        },
        2: {
            peripheral: "TIMA0",
            channel: "3",
        },
        3: {
            peripheral: "TIMA1",
            channel: "1",
        },
        4: {
            peripheral: "TIMG12",
            channel: "1",
        },
        5: {
            peripheral: "TIMG6",
            channel: "1",
        },
        6: {
            peripheral: "TIMG7",
            channel: "1",
        },
    },
    MSPM0H321X: {
        // N/A: Comp not available for this device
    },
    MSPM0C1105_C1106: {
        0: {
            peripheral: "TIMG0",
            channel: "1",
        },
        1: {
            peripheral: "TIMG1",
            channel: "1",
        },
        2: {
            peripheral: "TIMG2",
            channel: "1",
        },
    },
    MSPM0G511X: {
        1: {
            peripheral: "TIMA0",
            channel: "2",
        },
        2: {
            peripheral: "TIMA0",
            channel: "3",
        },
        3: {
            peripheral: "TIMG0",
            channel: "0",
        },
        4: {
            peripheral: "TIMG0",
            channel: "1",
        },
        5: {
            peripheral: "TIMG6",
            channel: "1",
        },
        6: {
            peripheral: "TIMG7",
            channel: "1",
        },
    },
    MSPM0L211X_L112X: {
        1: {
            peripheral: "TIMA0",
            channel: "2",
        },
        2: {
            peripheral: "TIMA0",
            channel: "3",
        },
        3: {
            peripheral: "TIMA0",
            channel: "1",
        },
        4: {
            peripheral: "TIMG0",
            channel: "1",
        },
        5: {
            peripheral: "TIMG1",
            channel: "1",
        },
        6: {
            peripheral: "TIMG2",
            channel: "1",
        },
    },
    MSPM0L210X: {
        // N/A: COMP not available for this device
    },
}

let index = Common.getDeviceFamily();

exports = {
    blankingSources  : blankingSources[index],
}
